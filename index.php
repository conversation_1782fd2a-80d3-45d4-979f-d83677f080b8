<?php
// P<PERSON> redirects from the original file, ensure they are at the very top
if (preg_match("/pcg.di.unimi.it/", $_SERVER["HTTP_HOST"], $m)) {
    Header("HTTP/1.1 302 Found");
    Header("Location: https://pcg.di.unimi.it/pcg.php");
    exit;
}

if (preg_match("/(random|pseudorandom|xorshift|xoroshiro|xoshiro).di.unimi.it/", $_SERVER["HTTP_HOST"], $m)) {
    Header("HTTP/1.1 302 Found");
    Header("Location: https://prng.di.unimi.it" . $_SERVER["REQUEST_URI"]);
    exit;
}

// Function to determine if a given path matches the current page, for active link highlighting
// (Modified for a single-page site with internal anchors)
function isActiveAnchor($anchor_id)
{
    if (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '#' . $anchor_id) !== false) {
        return 'active';
    }
    return '';
}

// Define the navigation items for this page's sections
// These correspond to the IDs of the H1/H2 tags on the page
$prngNavItems = [
    ['label' => 'Intro', 'id' => 'intro'],
    ['label' => '64-bit', 'id' => 'id-64-bit-generators'],
    ['label' => '32-bit', 'id' => 'id-32-bit-generators'],
    ['label' => '16-bit', 'id' => 'id-16-bit-generators'],
    ['label' => 'Congruential', 'id' => 'congruential-generators'],
    ['label' => 'Adoption', 'id' => 'adoption'],
    ['label' => 'Shootout', 'id' => 'shootout'],
    ['label' => 'Remarks', 'id' => 'remarks'],
];
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="keywords"
        content="rng, prng, xoshiro, xoroshiro, xorshift, pseudorandom number generator, random number generator">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>A PRNG shootout</title>

    <link href="https://fonts.googleapis.com/css2?family=Hack&family=Roboto+Mono:wght@400;700&display=swap"
        rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
    <link href="style.css" rel="stylesheet">
</head>

<body class="bg-dark text-light">

    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top shadow-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="https://prng.di.unimi.it/">
                <i class="bi bi-cpu-fill me-2"></i>A PRNG Shootout
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#prngNavbar"
                aria-controls="prngNavbar" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="prngNavbar">
                <ul class="navbar-nav ms-auto">
                    <?php foreach ($prngNavItems as $item): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActiveAnchor($item['id']); ?>"
                                href="#<?php echo $item['id']; ?>">
                                <?php echo $item['label']; ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </nav>

    <main class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-9">
                <div class="row">

                    <div class="col-lg-9">
                        <h1 class="first" id="intro">Introduction</h1>

                        <p>This page describes some new pseudorandom number generators (PRNGs) <a
                            href="https://vigna.di.unimi.it/">I</a> have been working on recently
                            with David Blackman, and a shootout comparing them with
                            other generators. Details about the generators can be found in our <a
                            href="https://vigna.di.unimi.it/papers.php#BlVSLPNG">paper</a>.
                            Information about my previous <code>xorshift</code>-based generators
                            can be found <a href="xorshift.php">here</a>, but they have been
                            entirely superseded by the new ones, which are faster <em>and</em>
                            better. As part of our study, we developed a very strong <a
                                href="hwd.php">test for Hamming-weight dependencies</a>
                            that gave a number of surprising results.

                        <h1 id="id-64-bit-generators">64-bit Generators</h1>

                        <P><a href="xoshiro256plusplus.c"><code>xoshiro256++</code></a>/<a
                                href="xoshiro256starstar.c"><code>xoshiro256**</code></a>
                            (XOR/shift/rotate) are our <strong>all-purpose</strong>
                            generators (not <em>cryptographically secure</em> generators, though,
                            like all PRNGs in these pages). They have excellent (sub-ns) speed, a state
                            space (256 bits) that is large enough for any parallel application, and
                            they pass all tests we are aware of. See the <a
                                href="https://vigna.di.unimi.it/papers.php#BlVSLPNG">paper</a>
                            for a discussion of their differences.

                        <p>If, however, one has to generate only 64-bit <strong>floating-point</strong> numbers
                            (by extracting the upper 53 bits) <a href="xoshiro256plus.c"><code>xoshiro256+</code></a> is
                            a slightly (&asymp;15%)
                            faster generator with analogous statistical properties. For general
                            usage, one has to consider that its lowest bits have low linear
                            complexity and will <a href="xorshift.php#testing">fail linearity tests</a>; however, low linear
                            complexity of the lowest bits can have hardly any impact in practice, and certainly has no
                            impact at all if you generate floating-point numbers using the upper bits (we computed a <a
                                href="https://vigna.di.unimi.it/papers.php#BlVSLPNG">precise
                                estimate</a> of the linear complexity of the lowest bits).

                        <p>If you are <strong>tight on space</strong>, <a
                                href="xoroshiro128plusplus.c"><code>xoroshiro128++</code></a>/<a
                                href="xoroshiro128starstar.c"><code>xoroshiro128**</code></a>
                            (XOR/rotate/shift/rotate) and <a href="xoroshiro128plus.c"><code>xoroshiro128+</code></a>
                            have the same
                            speed and use half of the space; the same comments apply. They are suitable only for
                            low-scale parallel applications; moreover, <code>xoroshiro128+</code>
                            exhibits a mild dependency in Hamming weights that generates a failure
                            after 5&thinsp;TB of output in <a href="hwd.php">our test</a>. We believe
                            this slight bias cannot affect any application.

                        <p>Finally, if for any reason (which reason?) you need <strong>more
                                state</strong>, we provide in the same
                            vein <a href="xoshiro512plusplus.c"><code>xoshiro512++</code></a> / <a
                                href="xoshiro512starstar.c"><code>xoshiro512**</code></a> / <a
                                href="xoshiro512plus.c"><code>xoshiro512+</code></a> and
                            <a href="xoroshiro1024plusplus.c"><code>xoroshiro1024++</code></a> / <a
                                href="xoroshiro1024starstar.c"><code>xoroshiro1024**</code></a> / <a
                                href="xoroshiro1024star.c"><code>xoroshiro1024*</code></a> (see the <a
                                href="https://vigna.di.unimi.it/papers.php#BlVSLPNG">paper</a>).

                        <p>All generators, being based on linear recurrences, provide <em>jump
                                functions</em> that make it possible to simulate any number of calls to
                            the next-state function in constant time, once a suitable <em>jump
                                polynomial</em> has been computed. We provide ready-made jump functions for
                            a number of calls equal to the square root of the period, to make it easy
                            generating non-overlapping sequences for parallel computations, and equal
                            to the cube of the fourth root of the period, to make it possible to
                            generate independent sequences on different parallel processors.

                        <p>We suggest to use <a href="splitmix64.c"><span
                                    style="font-variant: small-caps">SplitMix64</span></a> to initialize
                            the state of our generators starting from a 64-bit seed, as <a
                                href="https://dl.acm.org/citation.cfm?doid=1276927.1276928">research
                                has shown</a> that initialization must be performed with a generator
                            radically different in nature from the one initialized to avoid
                            correlation on similar seeds.

                        <h1 id="id-32-bit-generators">32-bit Generators</h1>

                        <P><a href="xoshiro128plusplus.c"><code>xoshiro128++</code></a>/<a
                                href="xoshiro128starstar.c"><code>xoshiro128**</code></a> are our
                            <strong>32-bit</strong> all-purpose generators, whereas <a
                                href="xoshiro128plus.c"><code>xoshiro128+</code></a> is
                            for floating-point generation. They are the 32-bit counterpart of
                            <code>xoshiro256++</code>, <code>xoshiro256**</code> and <code>xoshiro256+</code>, so
                            similar comments apply.
                            Their state is too small for
                            large-scale parallelism: their intended usage is inside embedded
                            hardware or GPUs. For an even smaller scale, you can use <a
                                href="xoroshiro64starstar.c"><code>xoroshiro64**</code></a> and <a
                                href="xoroshiro64star.c"><code>xoroshiro64*</code></a>. We not believe
                            at this point in time 32-bit generator with a larger state can be of
                            any use (but there are 32-bit <code>xoroshiro</code> generators of much larger size).
                        </p>

                        <p>All 32-bit generators pass all tests we are aware of, with the
                            exception of linearity tests (binary rank and linear complexity) for
                            <code>xoshiro128+</code> and <code>xoroshiro64*</code>: in this case,
                            due to the smaller number of output bits the low linear complexity of the
                            lowest bits is sufficient to trigger BigCrush tests when the output is bit-reversed.
                            Analogously to
                            the 64-bit case, generating 32-bit floating-point number using the
                            upper bits will not use any of the bits with <a href="xorshift.php#testing">low linear
                                complexity</a>.
                        </p>

                        <h1 id="id-16-bit-generators">16-bit Generators</h1>

                        <p>We do not suggest any particular 16-bit generator, but it is possible
                            to design relatively good ones using our techniques. For example,
                            Parallax has embedded in their <a href="https://www.parallax.com/propeller-2/">Propeller 2
                                microcontroller</a> multiple 16-bit
                            <code>xoroshiro32++</code> generators.

                        <h1 id="congruential-generators">Congruential Generators</h1>

                        <p>In case you are interested in 64-bit PRNGs based on congruential arithmetic, I provide
                            three instances of a
                            <a
                                href="https://groups.google.com/forum/#!searchin/sci.stat.math/Yet$20another$20rng%7Csort:date/sci.stat.math/p7aLW3TsJys/QGb1kti6kN0J">Marsaglia's
                                Multiply-With-Carry generators</a>,
                            <a href="MWC128.c"><code>MWC128</code></a>, <a href="MWC192.c"><code>MWC192</code></a>, and
                            <a href="MWC256.c"><code>MWC256</code></a>, for which I computed good constants. They are
                            some
                            of the fastest generator available, but they need 128-bit operations.

                        <p>Stronger theoretical guarantees are provided by the
                            <a href="https://www.math.ias.edu/~goresky/pdf/p1-goresky.pdf">generalized
                                multiply-with-carry generators defined by Goresky and Klapper</a>:
                            also in this case I provide two instances, <a href="GMWC128.c"><code>GMWC128</code></a> and
                            <a href="GMWC256.c"><code>GMWC256</code></a>, for which I computed good constants.
                            This generators, however, are about twice slower than MWC generators.
                        </p>


                        <h1 id="adoption">Adoption</h1>

                        <p>These are some of the languages that have adopted our generators, in no particular order.</p>

                        <h2 id="javascript">JavaScript</h2>

                        <p><a href="xorshift128plus.c"><code>xorshift128+</code></a> is presently used in the JavaScript engines of
                            <a
                                href="https://v8project.blogspot.com/2015/12/theres-mathrandom-and-then-theres.html">Chrome</a>,
                            <a href="https://nodejs.org/">Node.js</a>,
                            <a href="https://bugzilla.mozilla.org/show_bug.cgi?id=322529#c99">Firefox</a>,
                            <a href="https://bugs.webkit.org/show_bug.cgi?id=151641">Safari</a> and
                            <a
                                href="https://github.com/Microsoft/ChakraCore/commit/dbda0182dc0a983dfb37d90c05000e79b6fc75b0">Microsoft
                                Edge</a>.

                        <h2 id="rust">Rust</h2>

                        <p>The <a href="https://docs.rs/rand/latest/rand/rngs/struct.SmallRng.html">SmallRng</a> from
                            the <a href="https://docs.rs/rand/latest/rand/">rand</a>
                            crate is <a HREF="xoshiro256plusplus.c"><code>xoshiro256++</code></a> or <a
                                HREF="xoshiro128plusplus.c"><code>xoshiro128++</code></a>, depending
                            on the platform.

                        <h2 id="java-util-random"><code>java.util.random</code></h2>

                        <p>I worked with Guy Steele at the <a
                                href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/random/package-summary.html">new
                                family of PRNGs available in Java 17</a>. The family, called <a
                                href="https://vigna.di.unimi.it/papers.php#StVLXM">LXM</a>, uses <a
                                href="https://vigna.di.unimi.it/papers.php#StVCESGMCPNG">new, better
                                tables of multipliers for LCGs with power-of-two moduli</a>. Moreover,
                            <code>java.util.random</code> contains ready-to-use implementations of
                            <a HREF="xoroshiro128plusplus.c"><code>xoroshiro128++</code></a> and <a
                                HREF="xoshiro256plusplus.c"><code>xoshiro256++</code></a>.

                        <h2 id="net">.NET</h2>

                        <p>In version 6, Microsoft's .NET framework <a
                                href="https://devblogs.microsoft.com/dotnet/performance-improvements-in-net-6/">has
                                adopted</a>
                            <a HREF="xoshiro256starstar.c"><code>xoshiro256**</code></a> and <a
                                HREF="xoshiro128starstar.c"><code>xoshiro128**</code></a> as default PRNGs.

                        <h2 id="erlang">Erlang</h2>


                        <p>The parallel functional language <a href="https://www.erlang.org/">Erlang</a> implements <a
                                href="https://www.erlang.org/doc/man/rand.html">several
                                variants of <code>xorshift</code>/<code>xoroshiro</code>-based generators</a> adapted in
                            collaboration with Raimo Niskanen for Erlang's
                            58/59-bit arithmetic.

                        <h2 id="gnu-fortran">GNU FORTRAN</h2>

                        <p>GNU's <a href="https://gcc.gnu.org/fortran/">implementation of the FORTRAN language</a> <a
                                href="https://gcc.gnu.org/onlinedocs/gfortran/RANDOM_005fNUMBER.html">uses</a>
                            <a HREF="xoshiro256starstar.c"><code>xoshiro256**</code></a> as default PRNG.

                        <h2 id="julia">Julia</h2>

                        <p>The <a href="https://julialang.org/">Julia programming language</a> <a
                                href="https://docs.julialang.org/en/v1/stdlib/Random/">uses</a>
                            <a HREF="xoshiro256plusplus.c"><code>xoshiro256++</code></a> as default PRNG.

                        <h2 id="lua">Lua</h2>

                        <p>The scripting language <a href="https://www.lua.org/">Lua</a> <a
                                href="https://www.lua.org/manual/5.4/manual.html#pdf-math.random">uses</a> <a
                                HREF="xoshiro256starstar.c"><code>xoshiro256**</code></a> as default PRNG.

                        <h2 id="jpegxl">JPEG XL</h2>

                        <p>The <a href="https://jpeg.org/jpegxl/">JPEG XL standard</a> uses <a href="xorshift128plus.c"><code>xorshift128+</code></a>
                         to generate white noise images.

                        <h2 id="iot">IoT</h2>

                        <p>The IoT operating systems <a href="https://os.mbed.com/">Mbed</a> and <a
                                href="https://www.zephyrproject.org/">Zephyr</a> use
                            <a HREF="xoroshiro128plus.c"><code>xoroshiro128+</code></a> as default PRNG.


                        <h1 id="shootout">A PRNG Shootout</h1>

                        <p>I provide here a shootout of a few recent 64-bit PRNGs that are quite widely used.
                            The purpose is that of providing a consistent, reproducible assessment of two properties of
                            the generators: speed and quality.
                            The code used to perform the tests and all the output from statistical test suites is
                            available for download.

                        <h2 id="speed">Speed</h2>

                        <p>The speed reported in this page is the time required to emit 64
                            random bits, and the number of clock cycles required to generate a byte (thanks to the <a
                                href="https://icl.utk.edu/papi/">PAPI</a> library). If a generator is 32-bit in nature,
                            I glue two
                            consecutive outputs. Note that
                            I do not report results using GPUs or SSE instructions, with an exception for the very
                            common SFMT: for that to be
                            meaningful, I should have implementations for all generators.
                            Otherwise, with suitable hardware support I could just use AES in
                            counter mode and get 64 secure bits in 0.56&thinsp;ns (or just use <a
                                href="https://github.com/google/randen">Randen</a>). The tests were performed on a
                            12th Gen Intel&reg; Core&trade; i7-12700KF @3.60GHz using <code>gcc</code> 12.2.1.

                        <p>A few <i>caveats</i>:
                        <ul>
                            <li>There is some looping overhead, but subtracting it from the timings is not going to
                                be particularly meaningful due to instruction rescheduling, etc.
                            <li>Relative speed might be different on different CPUs and on different scenarios.
                            <li>I do not use <code>-march=native</code>, which can improve the timing of some generators
                                by vectorization or special instructions, because those improvements might not be
                                possible
                                when the generator is embedded in user code.
                            <li>Code has been compiled using <code>gcc</code>'s <code>-fno-unroll-loops</code>
                                option. This options is essential to get a sensible result: without it, the compiler
                                may perform different loop unrolling depending on the generator. Previosuly I was using
                                also
                                <code>-fno-move-loop-invariants</code>, which was essential in not giving generators
                                using several
                                large constants an advantage by preventing the compiler from loading them into
                                registers. However,
                                as of <code>gcc</code> 12.2.1 the compiler loads the constants into registers anyway, so
                                the
                                option is no longer used. Timings
                                with <a href="https://clang.llvm.org/"><code>clang</code></a> at the time of this
                                writing
                                are very close to those obtained with <code>gcc</code>.
                                If you find timings that are significantly better than those shown here on
                                comparable hardware, they are likely to be due to compiler artifacts (e.g.,
                                vectorization).
                            <li>Timings are taken running a generator for billions of times in a loop; but this is not
                                the way you use generators. Register
                                allocation might be very different when the generator is embedded in an application,
                                leading to constants being reloaded
                                or part of the state space being written to main memory at each iteration. These costs
                                do not appear in the benchmarks below.
                        </ul>

                        <p>To ease replicability, I distribute a <a href="harness.c"><em>harness</em></a> performing the
                            measurement. You just
                            have to define a <a href="xoroshiro128plus-speed.c"><code>next()</code></a> function and
                            include the harness. But the only realistic
                            suggestion is to try different generators in your application and see what happens.

                        <h2 id="quality">Quality</h2>

                        <p>This is probably the more <a href="random.png">elusive</a> property
                            of a PRNG. Here quality is measured using the powerful
                            BigCrush suite of tests. BigCrush is part of <a
                                href="https://simul.iro.umontreal.ca/testu01/tu01.html">TestU01</a>,
                            a monumental framework for testing PRNGs developed by Pierre L'Ecuyer
                            and Richard Simard (&ldquo;TestU01: A C library for empirical testing
                            of random number generators&rdquo;, <i>ACM Trans. Math. Softw.</i>
                            33(4), Article 22, 2007).

                        <p>I run BigCrush starting from 100 equispaced points of the state space
                            of the generator and collect <em>failures</em>&mdash;tests in which the
                            <i>p</i>-value statistics is outside the interval [0.001..0.999]. A failure
                            is <em>systematic</em> if it happens at all points.

                        <p>Note that TestU01 is a 32-bit test suite. Thus, two 32-bit integer values
                            are passed to the test suite for each generated 64-bit value. Floating point numbers
                            are generated instead by dividing the unsigned output of the generator by 2<sup>64</sup>.
                            Since this implies a bias towards the high bits (which is anyway a known characteristic
                            of TestU01), I run the test suite also on the <em>reverse</em>
                            generator. More detail about the whole process can be found in this <a
                                href="https://vigna.di.unimi.it/papers.php#VigEEMXGS">paper</a>.

                        <p>Beside BigCrush, I analyzed generators using a test for <a href="hwd.php">Hamming-weight
                                dependencies</a>
                            described in our <a href="https://vigna.di.unimi.it/papers.php#BlVNTHWD">paper</a>. As I
                            already remarked, our only
                            generator failing the test (but only after 5&thinsp;TB of output) is
                            <code>xoroshiro128+</code>.

                        <p>I report the period of each generator and its footprint in bits: a generator gives
                            &ldquo;bang-for-the-buck&rdquo;
                            if the base-2 logarithm of the period is close to the footprint. Note
                            that the footprint has been always padded to a multiple of 64, and it can
                            be significantly larger than expected because of padding and
                            cyclic access indices.

                        <div class="table-responsive">
                            <table id='test-table' class='table table-striped table-hover table-bordered caption-top'>
                                <caption>Comparison of PRNGs</caption>
                                <thead>
                                    <tr>
                                        <th>PRNG</th>
                                        <th class="text-end">Footprint (bits)</th>
                                        <th class="text-end">Period</th>
                                        <th class="text-end"><a
                                                href="https://simul.iro.umontreal.ca/testu01/tu01.html">BigCrush</a>
                                            Systematic Failures</th>
                                        <th class="text-end"><a href="https://prng.di.unimi.it/hwd.php">HWD failure</a>
                                        </th>
                                        <th class="text-end">ns/64 bits</th>
                                        <th class="text-end">cycles/B</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><a href="xoroshiro128plus.c"><code>xoroshiro128+</code></a>
                                        <td>128
                                        <td data-order="128">2<sup>128</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>5&thinsp;TB
                                        <td>0.80
                                        <td>0.36
                                    <tr>
                                        <td><a href="xoroshiro128plusplus.c"><code>xoroshiro128++</code></a>
                                        <td>128
                                        <td data-order="128">2<sup>128</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.90
                                        <td>0.40
                                    <tr>
                                        <td><a href="xoroshiro128starstar.c"><code>xoroshiro128**</code></a>
                                        <td>128
                                        <td data-order="128">2<sup>128</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.78
                                        <td>0.36
                                    <tr>
                                        <td><a href="xoshiro256plus.c"><code>xoshiro256+</code></a>
                                        <td>256
                                        <td data-order="256">2<sup>256</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.61
                                        <td>0.27
                                    <tr>
                                        <td><a href="xoshiro256plusplus.c"><code>xoshiro256++</code></a>
                                        <td>256
                                        <td data-order="256">2<sup>256</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.75
                                        <td>0.34
                                    <tr>
                                        <td><a href="xoshiro256starstar.c"><code>xoshiro256**</code></a>
                                        <td>256
                                        <td data-order="256">2<sup>256</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.75
                                        <td>0.34
                                    <tr>
                                        <td><a href="xoshiro512plus.c"><code>xoshiro512+</code></a>
                                        <td>512
                                        <td data-order="512">2<sup>512</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.68
                                        <td>0.30
                                    <tr>
                                        <td><a href="xoshiro512plusplus.c"><code>xoshiro512++</code></a>
                                        <td>512
                                        <td data-order="512">2<sup>512</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.79
                                        <td>0.36
                                    <tr>
                                        <td><a href="xoshiro512starstar.c"><code>xoshiro512**</code></a>
                                        <td>512
                                        <td data-order="512">2<sup>512</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.81
                                        <td>0.37
                                    <tr>
                                        <td><a href="xoroshiro1024star.c"><code>xoroshiro1024*</code></a>
                                        <td>1068
                                        <td data-order="1024">2<sup>1024</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.82
                                        <td>0.37
                                    <tr>
                                        <td><a href="xoroshiro1024plusplus.c"><code>xoroshiro1024++</code></a>
                                        <td>1068
                                        <td data-order="1024">2<sup>1024</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>1.01
                                        <td>0.46
                                    <tr>
                                        <td><a href="xoroshiro1024starstar.c"><code>xoroshiro1024**</code></a>
                                        <td>1068
                                        <td data-order="1024">2<sup>1024</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.98
                                        <td>0.44
                                    <tr>
                                        <td><a href="MWC128.c"><span style="font-variant: small-caps">MWC128</span></a>
                                        <td>128
                                        <td data-order="127">&asymp;2<sup>127</sup>
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.83
                                        <td>0.37
                                    <tr>
                                        <td><a href="MWC192.c"><span style="font-variant: small-caps">MWC192</span></a>
                                        <td>192
                                        <td data-order="127">&asymp;2<sup>191</sup>
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.42
                                        <td>0.19
                                    <tr>
                                        <td><a href="MWC256.c"><span style="font-variant: small-caps">MWC256</span></a>
                                        <td>256
                                        <td data-order="255">&asymp;2<sup>255</sup>
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.45
                                        <td>0.20
                                    <tr>
                                        <td><a href="GMWC128.c"><span
                                                    style="font-variant: small-caps">GMWC128</span></a>
                                        <td>128
                                        <td data-order="127">&asymp;2<sup>127</sup>
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>1.84
                                        <td>0.83
                                    <tr>
                                        <td><a href="GMWC256.c"><span
                                                    style="font-variant: small-caps">GMWC256</span></a>
                                        <td>256
                                        <td data-order="255">&asymp;2<sup>255</sup>
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>1.85
                                        <td>0.83
                                    <tr>
                                        <td><a href="https://pracrand.sourceforge.net/"><span
                                                    style="font-variant: small-caps">SFC64</span></a>
                                        <td>256
                                        <td data-order="64">&ge;2<sup>64</sup>
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.66
                                        <td>0.30
                                    <tr>
                                        <td><a href="splitmix64.c"><span
                                                    style="font-variant: small-caps">SplitMix64</span></a>
                                        <td>64
                                        <td data-order="64">2<sup>64</sup>
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.63
                                        <td>0.29
                                    <tr>
                                        <td><a href="https://pcg-random.org/">PCG 128 XSH RS 64 (LCG)</a>
                                        <td>128
                                        <td data-order="128">2<sup>128</sup>
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>1.70
                                        <td>0.77
                                    <tr>
                                        <td><a href="https://github.com/numpy/numpy">PCG64-DXSM (NumPy)</a>
                                        <td>128
                                        <td data-order="128">2<sup>128</sup>
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>1.11
                                        <td>0.50
                                    <tr>
                                        <td><a href="https://numerical.recipes/"><code>Ran</code></a>
                                        <td>192
                                        <td data-order="191">&#8776;2<sup>191</sup>
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>1.37
                                        <td>0.62
                                    <tr>
                                        <td><a href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/emt.html"><code>MT19937-64</code>
                                                (Mersenne Twister)</a>
                                        <td>20032
                                        <td data-order="19937">2<sup>19937</sup>&nbsp;&minus;&nbsp;1
                                        <td>LinearComp
                                        <td>&mdash;
                                        <td>1.36
                                        <td>0.62
                                    <tr>
                                        <td><a
                                                href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/SFMT/"><code>SFMT19937</code></a>
                                            (uses SSE2 instructions)
                                        <td>20032
                                        <td data-order="19937">2<sup>19937</sup>&nbsp;&minus;&nbsp;1
                                        <td>LinearComp
                                        <td>&mdash;
                                        <td>0.93
                                        <td>0.42
                                    <tr>
                                        <td><a
                                                href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/SFMT/"><code>SFMT607</code></a>
                                            (uses SSE2 instructions)
                                        <td>672
                                        <td data-order="607">2<sup>607</sup>&nbsp;&minus;&nbsp;1
                                        <td>MatrixRank, LinearComp
                                        <td>400 MB
                                        <td>0.78
                                        <td>0.34
                                    <tr>
                                        <td><a
                                                href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/TINYMT/index.html">Tiny
                                                Mersenne Twister</a> (64 bits)
                                        <td>256
                                        <td data-order="127">2<sup>127</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>90&thinsp;TB→
                                        <td>2.76
                                        <td>1.25
                                    <tr>
                                        <td><a
                                                href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/TINYMT/index.html">Tiny
                                                Mersenne Twister</a> (32 bits)
                                        <td>224
                                        <td data-order="127">2<sup>127</sup>&nbsp;&minus;&nbsp;1
                                        <td>CollisionOver, Run, SimPoker, AppearanceSpacings, MatrixRank, LinearComp,
                                            LongestHeadRun, Run of Bits (reversed)
                                        <td>40&thinsp;TB→
                                        <td>4.27
                                        <td>1.92
                                    <tr>
                                        <td><a
                                                href="https://dl.acm.org/doi/10.1145/1132973.1132974"><code>WELL512a</code></a>
                                        <td>544
                                        <td data-order="512">2<sup>512</sup>&nbsp;&minus;&nbsp;1
                                        <td>MatrixRank, LinearComp
                                        <td>3.5 PB
                                        <td>5.42
                                        <td>2.44
                                    <tr>
                                        <td><a
                                                href="https://dl.acm.org/doi/10.1145/1132973.1132974"><code>WELL1024a</code></a>
                                        <td>1056
                                        <td data-order="1024">2<sup>1024</sup>&nbsp;&minus;&nbsp;1
                                        <td>MatrixRank, LinearComp
                                        <td>&mdash;
                                        <td>5.30
                                        <td>2.38
                                </tbody>
                            </table>
                        </div>

                        <p>The following table compares instead two ways of generating floating-point numbers, namely
                            the 521-bit <a href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/SFMT/">dSFMT</a>,
                            which
                            generates directly floating-point numbers with 52 significant bits, and
                            <a href="xoshiro256plus.c"><code>xoshiro256+</code></a> followed by a standard conversion of
                            its upper bits to a floating-point number with 53 significant bits (see below).

                        <div class="table-responsive">
                            <table id='double-table' class='table table-striped table-hover table-bordered caption-top'>
                                <caption>Comparison of Floating–Point PRNGs</caption>
                                <thead>
                                    <tr>
                                        <th>PRNG</th>
                                        <th class="text-end">Footprint (bits)</th>
                                        <th class="text-end">Period</th>
                                        <th class="text-end"><a
                                                href="https://simul.iro.umontreal.ca/testu01/tu01.html">BigCrush</a>
                                            Systematic Failures</th>
                                        <th class="text-end"><a href="https://prng.di.unimi.it/hwd.php">HWD failure</a>
                                        </th>
                                        <th class="text-end">ns/64 bits</th>
                                        <th class="text-end">cycles/B</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><a href="xoshiro256plus.c"><code>xoshiro256+</code></a> (returns 53
                                            significant bits)
                                        <td>256
                                        <td data-order="256">2<sup>256</sup>&nbsp;&minus;&nbsp;1
                                        <td>&mdash;
                                        <td>&mdash;
                                        <td>0.92
                                        <td>3.40
                                    <tr>
                                        <td><a
                                                href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/SFMT/"><code>dSFMT</code></a>
                                            (uses SSE2 instructions, returns only 52 significant bits)
                                        <td>704
                                        <td data-order="521">2<sup>521</sup>&nbsp;&minus;&nbsp;1
                                        <td>MatrixRank, LinearComp
                                        <td>6&thinsp;TB
                                        <td>0.85
                                        <td>3.07
                                </tbody>
                            </table>
                        </div>

                        <p><code>xoshiro256+</code> is &asymp;8% slower than the dSFMT, but it has a doubled range of
                            output values, does not need any extra SSE instruction (can be programmed in Java, etc.),
                            has a much smaller footprint, and its upper bits do not fail any test.

                        <h1 id="remarks">Remarks</h1>

                        <h2 id="vectorization">Vectorization</h2>

                        <p>Some of the generators can be very easily vectorized, so that multiple instances can be run
                            in parallel to provide
                            fast bulk generation. Thanks to an interesting <a
                                href="https://github.com/JuliaLang/julia/issues/27614">discussion with the Julia
                                developers</a>,
                            I've become aware that AVX2 vectorizations of multiple instances of generators using the
                            <code>+</code>/<code>++</code> scrambler are impressively fast (links
                            below point at a speed test to be used with the <a href="harness.c">harness</a>, and the
                            result will be multiplied by 1000):

                        <div class="table-responsive">
                            <table id='vectorized-table'
                                class='table table-striped table-hover table-bordered caption-top'>
                                <caption>Comparison of Vectorized PRNGs</caption>
                                <thead>
                                    <tr>
                                        <th>PRNG</th>
                                        <th class="text-end">ns/64 bits</th>
                                        <th class="text-end">cycles/B</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><a href="xoroshiro128+-vect-speed.c"><code>xoroshiro128+</code></a> (4
                                            parallel instances)
                                        <td>0.36
                                        <td>0.14
                                    <tr>
                                        <td><a href="xoroshiro128++-vect-speed.c"><code>xoroshiro128++</code></a> (4
                                            parallel instances)
                                        <td>0.45
                                        <td>0.18
                                    <tr>
                                        <td><a href="xoshiro256+-vect-speed.c"><code>xoshiro256+</code></a> (8 parallel
                                            instances)
                                        <td>0.19
                                        <td>0.08
                                    <tr>
                                        <td><a href="xoshiro256++-vect-speed.c"><code>xoshiro256++</code></a> (8
                                            parallel instances)
                                        <td>0.26
                                        <td>0.09
                                </tbody>
                            </table>
                        </div>


                        <p>Note that sometimes convincing the compiler to vectorize is a
                            slightly quirky process: for example, on <code>gcc</code> 12.2.1 I have to use
                            <code>-O3 -fdisable-tree-cunrolli -march=native</code>
                            to vectorize <code>xoshiro256</code>-based generators
                            (<code>-O3</code> alone will not vectorize; thanks to to Chris Elrod for pointing me at
                            <code>-fdisable-tree-cunrolli</code>).

                        <h2>A long period does not imply high quality</h2>

                        <p>This is a common misconception. The generator <code>x++</code> has
                            period 2<sup><i>k</i></sup>, for any <i>k</i> &geq; 0, provided that <code>x</code> is
                            represented using <i>k</i> bits: nonetheless, it is a horrible generator.
                            The generator returning <i>k</i> &minus; 1 zeroes followed by a one has period
                            <i>k</i>.

                        <p>It is however important that the period is long enough. A first heuristic rule of thumb
                            is that if you need to use <i>t</i> values, you need a generator with period at least
                            <i>t</i>².
                            Moreover, if you run <i>n</i> independent computations starting at random seeds,
                            the sequences used by each computation should not overlap. Lack of overlapping does not
                            guarantee
                            lack of correlation, but overlapping sequences are certainly correlated.

                        <p>Now, given a generator with period <i>P</i>, the probability that <i>n</i> subsequences of
                            length <i>L</i> starting at random points in the state space
                            overlap <a href="https://vigna.di.unimi.it/papers.php#VigPORSPNG">is bounded by
                                <i>n</i>²<i>L</i>/<i>P</i></a>. If your generator has period 2<sup>256</sup> and you run
                            on 2<sup>64</sup> cores (you will never have them) a computation using 2<sup>64</sup>
                            pseudorandom numbers (you will never have the time)
                            the probability of overlap would be less than 1/2<sup>64</sup>.

                        <p>In other words: any generator with a period beyond
                            2<sup>256</sup> has a period that is
                            sufficient for every imaginable application. Unless there are other motivations (e.g.,
                            provably
                            increased quality), a generator with a larger period is only a waste of
                            memory (as it needs a larger state), of cache lines, and of
                            precious high-entropy random bits for seeding (unless you're using
                            small seeds, but then it's not clear why you would want a very long
                            period in the first place&mdash;the computation above is valid only if you seed all bits of
                            the state
                            with independent, uniformly distributed random bits).

                        <p>In case the generator provides a <em>jump function</em> that lets you skip through chunks of
                            the output in constant
                            time, even a period of 2<sup>128</sup> can be sufficient, as it provides 2<sup>64</sup>
                            non-overlapping sequences of length 2<sup>64</sup>.

                        <h2>Equidistribution</h2>

                        <p>Every 64-bit generator of ours with <var>n</var> bits of state scrambled
                            with <code>*</code> or <code>**</code> is <var>n</var>/64-dimensionally
                            equidistributed: every <var>n</var>/64-tuple of consecutive 64-bit
                            values appears exactly once in the output, except for the zero tuple
                            (and this is the largest possible dimension). Generators based on the
                            <code>+</code> or <code>++</code> scramblers are however only (<var>n</var>/64 &minus;
                            1)-dimensionally equidistributed: every (<var>n</var>/64 &minus;
                            1)-tuple of consecutive 64-bit values appears exactly 2<sup>64</sup>
                            times in the output, except for a missing zero tuple. The same considerations
                            apply to 32-bit generators.

                        <h2>Generating uniform doubles in the unit interval</h2>

                        <p>A standard double (64-bit) floating-point number in
                            <a href="https://en.wikipedia.org/wiki/IEEE_floating_point">IEEE floating point format</a>
                            has 52 bits of
                            significand, plus an implicit bit at the left of the significand. Thus,
                            the representation can actually store numbers with <em>53</em> significant binary digits.

                        <p>Because of this fact, in C99 a 64-bit unsigned integer <code>x</code> should be converted to
                            a 64-bit double
                            using the expression
                        <pre>
    #include &lt;stdint.h>

    (x >> 11) * 0x1.0p-53
</pre>
                        <p>In Java you can use almost the same expression for a (signed) 64-bit integer:
                        <pre>
    (x >>> 11) * 0x1.0p-53
</pre>


                        <p>This conversion guarantees that all dyadic rationals of the form <var>k</var> /
                            2<sup>&minus;53</sup>
                            will be equally likely. Note that this conversion prefers the high bits of <code>x</code>
                            (usually, a good idea), but you can alternatively
                            use the lowest bits.

                        <p>An alternative, multiplication-free conversion is
                        <pre>
    #include &lt;stdint.h>

    static inline double to_double(uint64_t x) {
       const union { uint64_t i; double d; } u = { .i = UINT64_C(0x3FF) &lt;&lt; 52 | x >> 12 };
       return u.d - 1.0;
    }
</pre>
                        <p>The code above cooks up by bit manipulation
                            a real number in the interval [1..2), and then subtracts
                            one to obtain a real number in the interval [0..1). If <code>x</code> is chosen uniformly
                            among 64-bit integers,
                            <code>d</code> is chosen uniformly among dyadic rationals of the form <var>k</var> /
                            2<sup>&minus;52</sup>. This
                            is the same technique used by generators providing directly doubles, such as the
                            <a href="https://dx.doi.org/10.1007/978-3-540-85912-3_26">dSFMT</a>.

                        <p>This technique is supposed to be fast, but on recent hardare it does not seem to give a
                            significant advantage.
                            More importantly, <em>you will be generating half the values you could actually
                                generate</em>.
                            The same problem plagues the dSFMT. All doubles generated will have the lowest significand
                            bit set to zero (I must
                            thank Raimo Niskanen from the Erlang team for making me notice this&mdash;a previous version
                            of this site
                            did not mention this issue).

                        <p>In Java you can obtain an analogous result using suitable static methods:
                        <pre>
    Double.longBitsToDouble(0x3FFL &lt;&lt; 52 | x >>> 12) - 1.0
</pre>

                        <p>To adhere to the principle of least surprise, my implementations now use the multiplicative
                            version, everywhere.

                        <p>Interestingly, these are not the only notions of &ldquo;uniformity&rdquo; you can come up
                            with. Another possibility
                            is that of generating 1074-bit integers, normalize and return the nearest value
                            representable as a
                            64-bit double (this is the theory&mdash;in practice, you will almost never
                            use more than two integers per double as the remaining bits would not be representable).
                            This approach guarantees that all
                            representable doubles could be in principle generated, albeit not every
                            returned double will appear with the same probability. A reference
                            implementation can be found <a href="random_real.c">here</a>. Note that unless your
                            generator has
                            at least 1074 bits of state and suitable equidistribution properties, the code above will
                            not do what you expect
                            (e.g., it might <em>never</em> return zero).

                    </div>

                    <div class="col-lg-3">
                        <aside class="pt-lg-4">

                            <h5 class="mt-4 mt-lg-0">C (64 bits)</h5>
                            <p>
                            <ul>
                                <li><a HREF="xoshiro256plusplus.c"><code>xoshiro256++</code></a>
                                <li><a HREF="xoshiro256starstar.c"><code>xoshiro256**</code></a>
                                <li><a HREF="xoshiro256plus.c"><code>xoshiro256+</code></a>
                                <li><a HREF="xoroshiro128plusplus.c"><code>xoroshiro128++</code></a>
                                <li><a HREF="xoroshiro128starstar.c"><code>xoroshiro128**</code></a>
                                <li><a HREF="xoroshiro128plus.c"><code>xoroshiro128+</code></a>
                                <li><a HREF="https://github.com/vigna/MRG32k3a">Testless <code>MRG32k3a</code></a>
                                <li><a HREF="MWC128.c"><code>MWC128</code></a> + <a HREF="mp.c"><code>mp.c</code></a>
                                <li><a HREF="MWC192.c"><code>MWC192</code></a> + <a HREF="mp.c"><code>mp.c</code></a>
                                <li><a HREF="MWC256.c"><code>MWC256</code></a> + <a HREF="mp.c"><code>mp.c</code></a>
                                <li><a HREF="GMWC128.c"><code>GMWC128</code></a> + <a HREF="mp.c"><code>mp.c</code></a>
                                <li><a HREF="GMWC256.c"><code>GMWC256</code></a> + <a HREF="mp.c"><code>mp.c</code></a>
                            </ul>

                            <h5 class="mt-4 mt-lg-0">C (32 bits)</h5>
                            <p>
                            <ul>
                                <li><a HREF="xoshiro128plusplus.c"><code>xoshiro128++</code></a>
                                <li><a HREF="xoshiro128starstar.c"><code>xoshiro128**</code></a>
                                <li><a HREF="xoshiro128plus.c"><code>xoshiro128+</code></a>
                                <li><a HREF="xoroshiro64starstar.c"><code>xoroshiro64**</code></a>
                                <li><a HREF="xoroshiro64star.c"><code>xoroshiro64*</code></a>
                            </ul>


                            <h5 class="mt-4 mt-lg-0">C++</h5>
										<p>
										<ul><li>Nessan Fitzmaurice provides an excellent <a href="https://nessan.github.io/xoshiro/">library</a>
										that supports arbitrary jumps.
										</ul>

                            <h5 class="mt-4 mt-lg-0">Rust</h5>
									<p><ul><li><a href="https://crates.io/crates/rand_xoshiro/">Rand
                                    project</a></ul>

                            <h5 class="mt-4 mt-lg-0">Java</h5>
									<p><ul>
	                            <li><a href="https://dsiutils.di.unimi.it">DSI utilities</a>

                            <p>
                            <ul>
                                <li><a
                                        HREF="https://dsiutils.di.unimi.it/docs/it/unimi/dsi/util/package-summary.html">Overview</a>
                                <li><a
                                        HREF="https://dsiutils.di.unimi.it/docs/it/unimi/dsi/util/XoShiRo256PlusPlusRandom.html"><code>xoshiro256++</code></a>
                                <li><a
                                        HREF="https://dsiutils.di.unimi.it/docs/it/unimi/dsi/util/XoShiRo256StarStarRandom.html"><code>xoshiro256**</code></a>
                                <li><a
                                        HREF="https://dsiutils.di.unimi.it/docs/it/unimi/dsi/util/XoShiRo256PlusRandom.html"><code>xoshiro256+</code></a>
                                <li><a
                                        HREF="https://dsiutils.di.unimi.it/docs/it/unimi/dsi/util/XoRoShiRo128PlusPlusRandom.html"><code>xoroshiro128++</code></a>
                                <li><a
                                        HREF="https://dsiutils.di.unimi.it/docs/it/unimi/dsi/util/XoRoShiRo128StarStarRandom.html"><code>xoroshiro128**</code></a>
                                <li><a
                                        HREF="https://dsiutils.di.unimi.it/docs/it/unimi/dsi/util/XoRoShiRo128PlusRandom.html"><code>xoroshiro128+</code></a>
                            </ul>
										<li><a HREF="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/random/package-summary.html"><code>java.util.random</code></a>
										<li><a HREF="https://gitbox.apache.org/repos/asf?p=commons-rng.git">Apache Commons</a>
                              <li><a HREF="https://github.com/vigna/MRG32k3a">Testless <code>MRG32k3a</code></a>
									</ul>
                            <h5 class="mt-4 mt-lg-0">Papers</h5>
                            <p>
                            <ul>
                                <li>The <a href="https://vigna.di.unimi.it/papers.php#BlVSLPNG">paper</a> introducing
                                    <code>xoshiro</code>/<code>xoroshiro</code>.
                                <li>The <a href="https://vigna.di.unimi.it/papers.php#BlVNTHWD">paper</a> describing our
                                    <a href="hwd.php">test for Hamming-weight dependencies</a>.
                                <li>The <a href="https://vigna.di.unimi.it/papers.php#VigHTLGMT">paper</a> discussing
                                    the defects of the Mersenne Twister family of PRNGs.
                                <li>The <a href="https://vigna.di.unimi.it/papers.php#VigPORSPNG">paper</a> discussing
                                    the probability of overlap of random subsequences.
                                <li>The <a href="https://vigna.di.unimi.it/papers.php#StVCESGMCPNG">paper</a> with new
                                    tables of multipliers for LCGs with power-of-two moduli.
                                <li>The <a href="https://vigna.di.unimi.it/papers.php#StVLXM">paper</a> presenting the
                                    family LXM of PRNGs.
                            </ul>

                            <h5 class="mt-4 mt-lg-0">Discussion</h5>

                            <p>There is a <a href="https://groups.google.com/group/prng">discussion group</a>
                                about this page. You can join or <a href="mailto:<EMAIL>">send a
                                    message</a>.

                            <h5 class="mt-4 mt-lg-0">Validation</h5>
                            <p><a href="https://validator.w3.org/check?uri=<?php echo urlencode($_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>"
                                    class="text-decoration-none">This is valid HTML5</a></p>
                        </aside>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-dark text-center py-3 mt-5 border-top border-secondary">
        <div class="container">
            <div class="row">
                <div class="col-12 text-light">
                    <p class="mb-0">&copy; <?php echo date("Y"); ?> Sebastiano Vigna. All rights reserved.</p>
                    <p class="mb-0">Built with <a href="https://getbootstrap.com/"
                            class="text-info text-decoration-none" target="_blank"
                            rel="noopener noreferrer">Bootstrap</a> and
                        <a href="https://php.net/" class="text-info text-decoration-none" target="_blank"
                            rel="noopener noreferrer">PHP</a>.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
        crossorigin="anonymous"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>

    <script>
        $(document).ready(function () {
            $('#test-table').DataTable({
                // Optional: Customize DataTables here
                paging: false,      // Disable pagination
                searching: false,   // Disable search box
                info: false,        // Disable "Showing X of Y entries" info
                ordering: true,     // Enable ordering (sorting)
                order: [[0, 'asc']], // Default sort by first column ascending
                columnDefs: [
                    { type: 'num', targets: [1, 5, 6] } // Treat columns 1, 5, 6 as numbers for sorting
                ]
            });

            $('#double-table').DataTable({
                // Optional: Customize DataTables here
                paging: false,      // Disable pagination
                searching: false,   // Disable search box
                info: false,        // Disable "Showing X of Y entries" info
                ordering: true,     // Enable ordering (sorting)
                order: [[0, 'asc']], // Default sort by first column ascending
                columnDefs: [
                    { type: 'num', targets: [1, 5, 6] } // Treat columns 1, 5, 6 as numbers for sorting
                ]
            });

            $('#vectorized-table').DataTable({
                // Optional: Customize DataTables here
                paging: false,      // Disable pagination
                searching: false,   // Disable search box
                info: false,        // Disable "Showing X of Y entries" info
                ordering: true,     // Enable ordering (sorting)
                order: [[0, 'asc']], // Default sort by first column ascending
                columnDefs: [
                    { type: 'num', targets: [1, 2] } // Treat columns 1, 2 as numbers for sorting
                ]
            });

            var $sections = $('h1[id]'); // Target h1 elements with an ID
            var $navLinks = $('.navbar-nav .nav-link');
            var navbarHeight = $('.navbar.sticky-top').outerHeight(); // Get the actual navbar height

            function getScrollMarginTop(element) {
                const computedStyle = window.getComputedStyle(element);
                // Parse the value as a float, defaulting to 0 if it's not a valid number (e.g., 'auto')
                return parseFloat(computedStyle.getPropertyValue('scroll-margin-top')) || 0;
            }

            // Function to update active link on scroll
            function updateActiveNavLink() {
                lastId = "intro";
                $sections.each(function () {
                    var sectionTop = $(this).offset().top - getScrollMarginTop(this) - navbarHeight; // Adjust for navbar height + a small buffer

                    if (sectionTop < 0) {
                        lastId = $(this).attr('id');
                    }
                });

                $navLinks.removeClass('active');
                $('a.nav-link[href="#' + lastId + '"]').addClass('active');
            }

            // Run on scroll
            document.body.addEventListener('scroll', updateActiveNavLink);

            // Enhance existing smooth scroll for internal links to immediately set active class
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    $navLinks.removeClass('active'); // Remove active from all
                    $(this).addClass('active');      // Add active to the clicked one
                });
            });

            // Initial check on page load to set the correct active link based on URL hash
            updateActiveNavLink();

            // Smooth scroll for internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1); // Remove '#'
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });

        }); // End of $(document).ready
    </script>