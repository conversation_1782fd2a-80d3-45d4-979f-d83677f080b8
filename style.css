/* Custom CSS for the PRNG site */

html,
body {
    height: 100%;
    /* Ensure html/body take full height for layout calculations */
    width: 100%;
    /* Ensure html/body take full width */
    overflow-x: hidden;
    /* Prevent horizontal scroll from minor layout shifts */
    font-family: 'Roboto Mono', monospace;
    /* Techy font for body */
    background-color: #222;
    /* Darker background */
    color: #eee;
    /* Light text */
}

/* Specific styling for internal anchors with sticky header */
h1[id],
h2[id],
h3[id],
h4[id],
h5[id],
h6[id] {
    scroll-margin-top: 70px;
}

/* This accounts for the potentially much larger obstruction you observe on mobile. */
@media (max-width: 991.98px) {

    /* This media query targets screens smaller than Bootstrap's 'lg' breakpoint */
    h1[id],
    h2[id],
    h3[id],
    h4[id],
    h5[id],
    h6[id],
    div.col[id] {
        scroll-margin-top: 400px;
    }
}

h1 {
    margin-top: 2rem;
}

h2 {
    font-size: 150%;
    margin-top: 1.5rem;
}

/* Heading styles for a tech look */
h1,
h2,
h3,
h4,
h5,
h6 {
    color: #00e676;
    /* Green accent for headings */
    font-family: 'Hack', monospace;
    /* Monospaced font for headings */
    /* Enhanced and layered glow effect */
    text-shadow:
        0 0 8px rgba(0, 230, 118, 0.7),
        /* Stronger, tighter glow */
        0 0 15px rgba(0, 230, 118, 0.3);
    /* Wider, softer glow */
    letter-spacing: 1px;
}

/* *** Specific styles for sidebar headings (significantly smaller) *** */
.col-lg-3 aside h1,
.col-lg-3 aside h2,
.col-lg-3 aside h3,
.col-lg-3 aside h4,
.col-lg-3 aside h5,
.col-lg-3 aside h6 {
    font-size: 1.25rem;
    margin-top: 1rem;
    margin-bottom: 0.5rem
}

/* Links */
a {
    color: #64b5f6;
    /* Blue accent for links */
    text-decoration: none;
    transition: color 0.2s ease-in-out;
}

a:hover {
    color: #90caf9;
    /* Lighter blue on hover */
    text-decoration: underline;
}


/* Navbar specific styling */
.navbar {
    background-color: #1a1a1a;
    /* Even darker navbar */
    border-bottom: 1px solid #333;
}

.navbar-brand,
.navbar-nav .nav-link {
    color: #00e676;
    /* Green branding and links */
    font-weight: bold;
    /* Reverting to the "OK" glow */
    text-shadow:
        0 0 8px rgba(0, 230, 118, 0.7),
        /* Stronger inner glow */
        0 0 15px rgba(0, 230, 118, 0.3);
    /* Wider, softer outer glow */
    letter-spacing: 1px;
    /* Subtle spacing for a digital look */
}

.navbar-nav .nav-link.active {
    background-color: rgba(0, 230, 118, 0.7);
    /* More opaque green background (70% opaque) */
    border-radius: 0.5rem;
    /* Slightly larger border radius for visual pop */
    color: #1a1a1a;
    /* Change text color to dark to contrast with bright green background */
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
    /* Add a subtle dark text shadow for definition on the bright background */
    /* NEW: Add more horizontal padding for better visual spacing */
    padding-left: 0.6rem;
    /* Increase left padding */
    padding-right: 0.6rem;
    /* Increase right padding */
}

/* Code and pre-formatted text */
pre,
code,
samp {
    /*font-family: 'Hack', monospace; /* Use Hack font consistently */
    /*font-size: 0.9em;*/
    background-color: #333;
    /* Dark background for code blocks */
    color: #f0f0f0;
    /* Light text */
    border: 1px solid #555;
    border-radius: 0.25rem;
    padding: 0.2em 0.4em;
}

pre {
    padding: 1em;
    display: block;
    /* Ensures pre takes full width */
    overflow-x: auto;
    /* Horizontal scroll for long lines */
}

/* Blockquote for emphasis */
blockquote {
    border-left: 5px solid #00e676;
    padding-left: 1em;
    margin-left: 0;
    color: #bbb;
}

/* Table styling for DataTables */
.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background-color: #00e676;
    color: #1a1a1a;
    border-color: #00e676;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: #333;
    color: #00e676;
    border-color: #00e676;
}

/* Custom styling for specific elements */
.alert-warning {
    background-color: #ffc1071a;
    /* Very subtle yellow background */
    color: #ffc107;
    /* Yellow text */
    border-color: #ffc107;
}

.alert-info {
    background-color: #0d6efd1a;
    /* Very subtle blue background */
    color: #0d6efd;
    /* Blue text */
    border-color: #0d6efd;
}

.small-caps {
    font-variant: small-caps;
}

/* Responsive table styling */
.table-responsive {
    margin-top: 2rem;
    margin-bottom: 2rem;
    border: 1px solid #555;
    /* Subtle border for the responsive container */
    border-radius: 0.25rem;
}

/* Adjust column width for small screens if needed, DataTables handles most */
/* For a truly techy look, you might consider a custom scrollbar for pre/code */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #333;
}

::-webkit-scrollbar-thumb {
    background: #00e676;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #00f08e;
}


/* General table styling */
table.dataTable {
    background-color: #1a1a1a;
    /* Dark background for the entire table */
    color: #eee;
    /* Light text color for table body */
    /* FORCE separate borders to allow individual cell borders and border-radius */
    border-collapse: separate;
    border-spacing: 0;
    /* FORCE no space between cells */
    width: 100%;
    /* Ensure table takes full width */
    border-radius: 0.25rem;
    /* Apply overall rounded corners to the table */
    overflow: hidden;
    /* Ensures child elements' corners respect the parent's border-radius */
    border: none;
    /* Remove any default outer border */
}

/* Table header styling (this section was already working well) */
table.dataTable thead th,
table.dataTable thead td {
    background-color: #00e676;
    /* Fluorescent green background for headers */
    color: #1a1a1a;
    /* Dark text on green background for contrast */
    border-bottom: 1px solid #00f08e;
    /* Slightly lighter green border below header */
    padding: 0.75rem 1rem;
    /* Adequate padding for header cells */
    text-align: left;
    /* Align header text to the left */
    font-weight: bold;
    text-shadow: 0 0 5px rgba(0, 230, 118, 0.5);
    /* Subtle glow */
}


.table-striped>tbody>tr:nth-child(odd) {
    background-color: #333;
}

.table-striped>tbody>tr:nth-child(odd):hover {
    background-color: #666;
}

.table-striped>tbody>tr:nth-child(odd)>td {
    background-color: inherit;
    color: white;
    border: 1px solid #3a3a3a;
}

.table-striped>tbody>tr:nth-child(even) {
    background-color: #222;
}

.table-striped>tbody>tr:nth-child(even):hover {
    background-color: #666;
}

.table-striped>tbody>tr:nth-child(even)>td {
    background-color: inherit;
    color: white;
    border: 1px solid #3a3a3a;
}

/* Links within table headers */
table.dataTable thead th a,
table.dataTable thead th a:visited {
    color: #1a1a1a;
    /* Dark text for links in headers */
    text-decoration: underline;
    /* Keep underline for visibility */
}

table.dataTable thead th a:hover {
    text-decoration: none;
}

/* Styling for the table caption (if used as the table title) */
table.dataTable caption {
    color: #00e676;
    /* Make the caption text fluorescent green */
    text-align: left;
    /* Align caption to the left */
    padding: 0.75rem 1rem;
    /* Consistent padding with cells */
    font-size: 1.2em;
    /* Make it stand out as a title */
    font-weight: bold;
    text-shadow: 0 0 5px rgba(0, 230, 118, 0.5);
}