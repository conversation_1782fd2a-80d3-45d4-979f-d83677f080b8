Intel(R) Architecture Code Analyzer Version -  v3.0-28-g1ba2cbb build date: 2017-10-23;16:42:45
Analyzed File -  ./xoroshiro128**-iaca
Binary Format - 64Bit
Architecture  -  SKL
Analysis Type - Throughput

Throughput Analysis Report
--------------------------
Block Throughput: 3.50 Cycles       Throughput Bottleneck: Dependency chains
Loop Count:  25
Port Binding In Cycles Per Iteration:
--------------------------------------------------------------------------------------------------
|  Port  |   0   -  DV   |   1   |   2   -  D    |   3   -  D    |   4   |   5   |   6   |   7   |
--------------------------------------------------------------------------------------------------
| Cycles |  2.2     0.0  |  2.3  |  1.3     1.0  |  1.4     1.0  |  2.0  |  2.3  |  2.2  |  1.3  |
--------------------------------------------------------------------------------------------------

DV - Divider pipe (on port 0)
D - Data fetch pipe (on ports 2 and 3)
F - Macro Fusion with the previous instruction occurred
* - instruction micro-ops not bound to a port
^ - Micro Fusion occurred
# - ESP Tracking sync uop was issued
@ - SSE instruction followed an AVX256/AVX512 instruction, dozens of cycles penalty is expected
X - instruction not supported, was not accounted in Analysis

| Num Of   |                    Ports pressure in cycles                         |      |
|  Uops    |  0  - DV    |  1   |  2  -  D    |  3  -  D    |  4   |  5   |  6   |  7   |
-----------------------------------------------------------------------------------------
|   1      |             |      | 0.6     0.6 | 0.4     0.4 |      |      |      |      | mov rdx, qword ptr [rip+0x200b41]
|   1*     |             |      |             |             |      |      |      |      | mov rcx, rdx
|   2^     |             | 0.3  | 0.4     0.4 | 0.6     0.6 |      | 0.7  |      |      | xor rcx, qword ptr [rip+0x200b3f]
|   1      |             | 0.7  |             |             |      | 0.3  |      |      | lea rax, ptr [rdx+rdx*4]
|   1      | 0.2         |      |             |             |      |      | 0.8  |      | rorx rdx, rdx, 0x28
|   1      | 0.8         |      |             |             |      |      | 0.2  |      | rorx rax, rax, 0x39
|   1      |             | 0.3  |             |             |      | 0.7  |      |      | lea rax, ptr [rax+rax*8]
|   1*     |             |      |             |             |      |      |      |      | mov rsi, rcx
|   1      | 0.2         | 0.3  |             |             |      | 0.3  | 0.2  |      | xor rdx, rcx
|   1      | 0.2         |      |             |             |      |      | 0.8  |      | rorx rcx, rcx, 0x1b
|   1      | 0.8         |      |             |             |      |      | 0.2  |      | shl rsi, 0x10
|   2^     |             |      |             |             | 1.0  |      |      | 1.0  | mov qword ptr [rip+0x200b14], rcx
|   1      |             | 0.7  |             |             |      | 0.3  |      |      | xor rdx, rsi
|   2^     |             |      | 0.3         | 0.4         | 1.0  |      |      | 0.3  | mov qword ptr [rip+0x200b02], rdx
Total Num Of Uops: 17
