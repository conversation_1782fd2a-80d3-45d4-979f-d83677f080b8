<?php
// P<PERSON> redirects from the original file, ensure they are at the very top
if (preg_match("/pcg.di.unimi.it/", $_SERVER["HTTP_HOST"], $m)) {
    Header("HTTP/1.1 302 Found");
    Header("Location: https://pcg.di.unimi.it/pcg.php");
    exit;
}

if (preg_match("/(random|pseudorandom|xorshift|xoroshiro|xoshiro).di.unimi.it/", $_SERVER["HTTP_HOST"], $m)) {
    Header("HTTP/1.1 302 Found");
    Header("Location: https://prng.di.unimi.it" . $_SERVER["REQUEST_URI"]);
    exit;
}

// Function to determine if a given path matches the current page, for active link highlighting
// (Modified for a single-page site with internal anchors)
function isActiveAnchor($anchor_id)
{
    if (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '#' . $anchor_id) !== false) {
        return 'active';
    }
    return '';
}

// Define the navigation items for this page's sections
// These correspond to the IDs of the H1/H2 tags on the page
$prngNavItems = [
    ['label' => 'Older Scrambled Linear Generators', 'id' => 'generators'],
    ['label' => 'Testing Lowest Bits in Isolation', 'id' => 'testing']
];
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="keywords"
        content="rng, prng, xoshiro, xoroshiro, xorshift, pseudorandom number generator, random number generator">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>Older Scrambled Linear Generators</title>

    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap"
        rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
    <link href="style.css" rel="stylesheet">
</head>

<body class="bg-light text-dark">

    <nav class="navbar navbar-expand-lg navbar-light bg-light sticky-top shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand" href="https://prng.di.unimi.it/">
                <i class="bi bi-cpu-fill me-2"></i> PRNG Shootout
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#prngNavbar"
                aria-controls="prngNavbar" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="prngNavbar">
                <ul class="navbar-nav ms-auto">
                    <?php foreach ($prngNavItems as $item): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActiveAnchor($item['id']); ?>"
                                href="#<?php echo $item['id']; ?>">
                                <?php echo $item['label']; ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </nav>

    <main class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-9">
                <div class="row">

                    <div class="col-lg-9">

                        <h1 class="first" id="generators">Older Scrambled Linear Generators</h1>

                        <h2><code>xorshift*</code></h2>

                        <P><a href="https://vigna.di.unimi.it/papers.php#VigEEMXGS"><code>xorshift*</code></a>
                            generators are fast, good-quality PRNGs (pseudorandom number
                            generators) obtained by scrambling the output of a Marsaglia <a
                                href="https://www.jstatsoft.org/v08/i14/"><code>xorshift</code></a>
                            generator with a 64-bit invertible multiplier (as suggested by
                            Marsaglia in his paper). They pass BigCrush from <a
                                href="https://simul.iro.umontreal.ca/testu01/tu01.html">TestU01</a>, but their
                            lowest bits have mild linear artifacts (like the Mersenne Twister,
                            WELL, etc.), and will fail linearity tests (see last section). This has no impact in
                            practice, and has no impact at all if you generate floating-point
                            numbers using the highest 53 bits. The exact linear complexity of the
                            lower bits can be estimated using results from <a
                                href="https://vigna.di.unimi.it/papers.php#BlVSLPNG">this paper</a>.
                            They are also affected by Hamming-weight dependencies, and should
                            be considered obsolete.

                        <h2><code>xorshift+</code></h2>

                        <P><a href="https://vigna.di.unimi.it/papers.php#VigFSMXG"><code>xorshift+</code></a>
                            generators are a 64-bit version of Saito and Matsumoto's <a
                                href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/XSADD/"><code>XSadd</code></a>
                            generator. Instead of using a multiplication, we return the sum (in
                            <b>Z</b>/2<sup>64</sup><b>Z</b>)
                            of two consecutive output of a <code>xorshift</code>
                            generator. As in the previous case, the generators pass BigCrush from TestU01, but again
                            their
                            lowest bits have mild (in fact, even milder) linear artifacts. Albeit superseded by <a
                                href="index.php">our new generators</a>,
                            <code>xorshift128+</code> is presently used in the JavaScript engines of
                            <a
                                href="https://v8project.blogspot.com/2015/12/theres-mathrandom-and-then-theres.html">Chrome</a>,
                            <a href="https://nodejs.org/">Node.js</a>,
                            <a href="https://bugzilla.mozilla.org/show_bug.cgi?id=322529#c99">Firefox</a>,
                            <a href="https://bugs.webkit.org/show_bug.cgi?id=151641">Safari</a> and
                            <a
                                href="https://github.com/Microsoft/ChakraCore/commit/dbda0182dc0a983dfb37d90c05000e79b6fc75b0">Microsoft
                                Edge</a>.
                            <code>xorshift128+</code> is a weaker generator than, say, <code>xoroshiro128+</code>, as it
                            fails
                            <a href="hwd.php">this test</a> after 6 GB of output.

                        <h1 id="testing">Testing Lowest Bits in Isolation</h1>

                        <p>In the documentation of generators with <em>weak scramblers</em> such as
                            <a href="xoshiro256plus.c"><code>xoshiro256+</code></a> it is mentioned
                            that the lowest bits will fail linearity tests, even if the whole
                            generator does not fail BigCrush. Here I try to explain in detail what this means to
                            non-experts.

                        <p>First of all, note these failures are not specific features of <code>xoroshiro128+</code> or
                            any
                            other generator I designed: all generators with a linear engine and a weak scrambler
                            have the same problem: for example, <code>Ranq1</code> from the third edition of &ldquo;<a
                                href="https://numerical.recipes/">Numerical Recipes</a>&rdquo;,
                            <a href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/XSADD/">XSadd</a>, and so on.

                        <p>The output of a linear generator satisfies some linear relations
                            between the bits. Moreover, the linear complexity of its bits (i.e.,
                            the lowest degree of a <a
                                href="https://en.wikipedia.org/wiki/Linear-feedback_shift_register">linear-feedback
                                shift register</a> representing a bit) will be quite low. The result is
                            that all linear generators (<a
                                href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/emt.html">Mersenne Twister</a>,
                            <a href="https://www.iro.umontreal.ca/~panneton/WELLRNG.html">WELL</a>, etc.) fail two basic
                            <em>linearity tests</em> which were designed to &ldquo;catch&rdquo; them:
                            the binary-rank test and the linear-complexity test (these are called MatrixRank
                            and LinearComp in <a href="https://simul.iro.umontreal.ca/testu01/tu01.html">TestU01</a>;
                            the second one is actually a combination of three tests&mdash;see the <a
                                href="https://simul.iro.umontreal.ca/testu01/guideshorttestu01.pdf">user
                                guide</a>).
                            Researchers working on linear generators consider these failures irrelevant to
                            applications, and this explains why, for example, the <a
                                href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/SFMT/">SFMT</a>
                            is the stock PRNG of Python, C, C++, etc. in spite of failing such
                            tests. We consider them of some importance, and that's why we design
                            scramblers. If you want to understand the influence of linearity
                            in applications, I <a href="https://vigna.di.unimi.it/papers.php#VigHTLGMT">provide an
                                insightful example</a>.

                        <p>When you scramble a linear generator using a sum or a
                            multiplication, its lowest bit (two bits at least, in case of a
                            multiplication) has the same linear complexity of the base generator,
                            and then complexity grows as you move towards the high bits (we computed a <a
                                href="https://vigna.di.unimi.it/papers.php#BlVSLPNG">precise
                                estimate</a> of the linear complexity of the lowest bits). The
                            complexity grows so quickly that often the influence of the lowest bits
                            is not sufficient to trigger failure in linearity tests: this is what happens, for instance,
                            with
                            our 64-bit generators, for which one submits the two 32-bit halves of the
                            output in sequence to TestU01.

                        <p>However, as noted in the documentation bits of low degree will fail linearity
                            tests when tested in isolation (or almost isolation). There are a few ways to
                            obtain this result.

                        <ol>
                            <li>If you are proficient in TestU01, you can easily <a href="xoroshiro128+-lower32.out">run
                                    those tests</a> <a href="xoroshiro128+-lower32.c">tuning the
                                    parameters</a> (usually, <var>r</var> and <var>s</var>) so they examine the bit of
                                interest in isolation.

                            <li>The linear-complexity test (LinearComp) in BigCrush is applied in isolation to the
                                highest bit and to the third lowest bit (you can see this from the
                                parameters of the test suite contained in the <a
                                    href="https://simul.iro.umontreal.ca/testu01/guideshorttestu01.pdf">user
                                    guide</a>). Thus, to test in isolation, say, the lowest bit, it is
                                sufficient to pass it to TestU01 continuously as the highest bit.
                                There are many ways to do this: you can rotate right by one position
                                the 64-bit output, and test just the upper 32 bits; or reverse the
                                output (inverse the bit direction) and test just the upper 32 bits; or
                                take the lower 32 bits and reverse them. All these transformations will
                                result in failures of the LinearComp test (for weak scramblers).
                                Rotating and taking the upper bits is possibly the easiest approach, as
                                you can test easily also the second, third lowest bit, etc.

                            <li>The binary-rank test (MatrixRank) in BigCrush has several instances, but
                                three of them (out of six) are applied to the 4, 5, and 30 highest bits,
                                so you can use the same trick as before. The various methods however are
                                technically no longer equivalent, as reversing will put in the highest position
                                the bits of lower complexity.

                            <li>For the binary-rank test you have also a lazy man's option: <a
                                    href="https://pracrand.sourceforge.net/">PractRand</a> will apply directly
                                the binary-rank test for you on the lowest bit only (run it with options
                                <code>-tf 2 -te 1</code>). It will fail immediately on all generators with
                                low-complexity lowest bits. If you wait enough, you will see failures
                                also on the whole output, as PractRand will run the test with progressively
                                larger matrices, and at some point the influence of the lowest bits will
                                be sufficient to bias the rank of the overall output. This is why, for example,
                                the <a href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/TINYMT/index.html">Tiny
                                    Mersenne Twister</a> or
                                our weakly scrambled generators pass BigCrush but fail the binary-rank test
                                in PractRand.
                        </ol>

                        <p>Of course you can do the opposite: extract a subset of 32 bits
                            <em>not</em> containing the lowest bits, and test it (possibly reversed).
                            It will of course pass the linearity tests of BigCrush, PractRand, etc.


                        <p>The 32-bit generators with weak scramblers (e.g.,
                            <a href="xoshiro128plus.c"><code>xoshiro128+</code></a>) do not have
                            enough high-complexity bits to &ldquo;hide&rdquo; the low linear
                            complexity of the lowest bits (as there is no interleaving); hence the
                            failures in MatrixRank and LinearComp when you just reverse the output.
                            As in the 64-bit case, a subset of bits not containing the
                            lowest ones will pass all linearity tests.

                        <p>Remember, however, that simple linear generators with sparse transition
                            matrices suffer also from <a href="hwd.php">Hamming-weight dependencies</a>.
                    </div>

                    <div class="col-lg-3">
                        <aside class="pt-lg-4">
                            <h5 class="mt-4 mt-lg-0">C code</h5>
                            <p>
                            <ul>
                                <li><a HREF="xorshift128plus.c"><code>xorshift128+</code></a>
                                <li><a HREF="xorshift1024star.c"><code>xorshift1024*&phi;</code></a>
                            </ul>

                            <h5>Java code (<a href="https://dsiutils.di.unimi.it">DSI utilities</a>)</h5>
                            <p>
                            <ul>
                                <li><a
                                        HREF="https://dsiutils.di.unimi.it/docs/it/unimi/dsi/util/XorShift128PlusRandom.html"><code>xorshift128+</code></a>
                                <li><a
                                        HREF="https://dsiutils.di.unimi.it/docs/it/unimi/dsi/util/XorShift1024StarPhiRandom.html"><code>xorshift1024*&phi;</code></a>
                            </ul>

                            <h5>Papers</h5>
                            <p>
                            <ul>
                                <li>The original <a href="https://www.jstatsoft.org/v08/i14/">paper</a> by Marsaglia.
                                <li>The <a href="https://vigna.di.unimi.it/papers.php#VigEEMXGS">paper</a> studying
                                    <code>xorshift*</code> generators in detail.
                                <li>The <a href="https://vigna.di.unimi.it/papers.php#VigFSMXG">paper</a> studying
                                    <code>xorshift+</code> generators in detail.
                            </ul>

                            <h5>Validation</h5>
                            <p><a href="https://validator.w3.org/check?uri=<?php echo urlencode($_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>"
                                    class="text-decoration-none">This is valid HTML5</a></p>
                        </aside>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-dark text-center py-3 mt-5 border-top border-secondary">
        <div class="container">
            <div class="row">
                <div class="col-12 text-light">
                    <p class="mb-0">&copy; <?php echo date("Y"); ?> Sebastiano Vigna. All rights reserved.</p>
                    <p class="mb-0">Built with <a href="https://getbootstrap.com/"
                            class="text-info text-decoration-none" target="_blank"
                            rel="noopener noreferrer">Bootstrap</a> and
                        <a href="https://php.net/" class="text-info text-decoration-none" target="_blank"
                            rel="noopener noreferrer">PHP</a>.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
        crossorigin="anonymous"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>

    <script>
        $(document).ready(function () {
            var $sections = $('h1[id]'); // Target h1 elements with an ID
            var $navLinks = $('.navbar-nav .nav-link');
            var navbarHeight = $('.navbar.sticky-top').outerHeight(); // Get the actual navbar height

            // Function to update active link on scroll
            function updateActiveNavLink() {
                lastId = "intro";
                $sections.each(function () {
                    var sectionTop = $(this).offset().top - navbarHeight - 10; // Adjust for navbar height + a small buffer

                    if (sectionTop < 0) {
                        lastId = $(this).attr('id');
                    }
                });

                $navLinks.removeClass('active');
                $('a.nav-link[href="#' + lastId + '"]').addClass('active');
            }

            // Run on scroll
            document.body.addEventListener('scroll', updateActiveNavLink);

            // Enhance existing smooth scroll for internal links to immediately set active class
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    $navLinks.removeClass('active'); // Remove active from all
                    $(this).addClass('active');      // Add active to the clicked one
                });
            });

            // Initial check on page load to set the correct active link based on URL hash
            updateActiveNavLink();

            // Smooth scroll for internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1); // Remove '#'
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }); // End of $(document).ready
    </script>