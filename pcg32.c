// *Really* minimal PCG32 code / (c) 2014 <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> / pcg-random.org
// Licensed under Apache License 2.0 (NO WARRANTY, etc. see website)

#include <stdint.h>

/* Minimal PCG32 implementation from http://www.pcg-random.org/download.html.
   The state must be initialized with a seed. Different odd increments will
   generate different streams. */

struct {
	uint64_t state;
	uint64_t inc;
} pcg32;

uint32_t next() {
	uint64_t oldstate = pcg32.state;
	// Advance internal state
	pcg32.state = oldstate * 6364136223846793005ULL + (pcg32.inc|1);
	// Calculate output function (XSH RR), uses old state for max ILP
	uint32_t xorshifted = ((oldstate >> 18u) ^ oldstate) >> 27u;
	uint32_t rot = oldstate >> 59u;
	return (xorshifted >> rot) | (xorshifted << ((-rot) & 31));
}
