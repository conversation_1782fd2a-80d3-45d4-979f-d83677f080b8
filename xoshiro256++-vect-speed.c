/*  Written in 2019 by <PERSON><PERSON> (<EMAIL>)

To the extent possible under law, the author has dedicated all copyright
and related and neighboring rights to this software to the public domain
worldwide.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES

WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR
IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE. */

#include <stdint.h>

// You can play with this value on your architecture
#define XOSHIRO256_UNROLL (8)

/* The current state of the generators. */
static uint64_t s[4][XOSHIRO256_UNROLL];

static __inline uint64_t rotl(const uint64_t x, int k) {
	return (x << k) | (x >> (64 - k));
}

#ifdef HARNESS_DOUBLE
double result[1000];
#else
uint64_t result[1000];
#endif

#ifdef HARNESS_DOUBLE
static inline double next(double * const restrict array, int len)
#else
static inline uint64_t next(uint64_t * const restrict array, int len)
#endif
{
 	uint64_t t[XOSHIRO256_UNROLL];

	for(int b = 0; b < len; b += XOSHIRO256_UNROLL) {
#ifdef HARNESS_DOUBLE
		for(int i = 0; i < XOSHIRO256_UNROLL; i++) array[b + i] = ((rotl(s[0][i] + s[3][i], 23) + s[0][i]) >> 11) * 0x1.0p-53;
#else
		for(int i = 0; i < XOSHIRO256_UNROLL; i++) array[b + i] = rotl(s[0][i] + s[3][i], 23) + s[0][i];
#endif

		for(int i = 0; i < XOSHIRO256_UNROLL; i++) t[i] = s[1][i] << 17;

		for(int i = 0; i < XOSHIRO256_UNROLL; i++) s[2][i] ^= s[0][i];
		for(int i = 0; i < XOSHIRO256_UNROLL; i++) s[3][i] ^= s[1][i];
		for(int i = 0; i < XOSHIRO256_UNROLL; i++) s[1][i] ^= s[2][i];
		for(int i = 0; i < XOSHIRO256_UNROLL; i++) s[0][i] ^= s[3][i];

		for(int i = 0; i < XOSHIRO256_UNROLL; i++) s[2][i] ^= t[i];

		for(int i = 0; i < XOSHIRO256_UNROLL; i++) s[3][i] = rotl(s[3][i], 45);
	}

	// This is just to avoid dead-code elimination
	return array[0] + array[len - 1];
}

#define INIT for(int i = 0; i < XOSHIRO256_UNROLL; i++) s[0][i] = 1 << i;
#define NEXT next(result, 1000);
#include "harness.c"
