/*  Written in 2019 by <PERSON><PERSON> (<EMAIL>)

To the extent possible under law, the author has dedicated all copyright
and related and neighboring rights to this software to the public domain
worldwide.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES

WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR
IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE. */

#include <stdint.h>

// You can play with this value on your architecture
#define XOROSHIRO128_UNROLL (4)

/* The current state of the generators. */
static uint64_t s[2][XOROSHIRO128_UNROLL];

static __inline uint64_t rotl(const uint64_t x, int k) {
	return (x << k) | (x >> (64 - k));
}

uint64_t result[1000];

static inline uint64_t next(uint64_t * const restrict array, int len) {
 	uint64_t s1[XOROSHIRO128_UNROLL];

	for(int b = 0; b < len; b += XOROSHIRO128_UNROLL) {
		for(int i = 0; i < XOROSHIRO128_UNROLL; i++) array[b + i] = s[0][i] + s[1][i];
		for(int i = 0; i < XOROSHIRO128_UNROLL; i++) s1[i] = s[0][i] ^ s[1][i];
		for(int i = 0; i < XOROSHIRO128_UNROLL; i++) s[0][i] = rotl(s[0][i], 24) ^ s1[i] ^ (s1[i] << 16);
		for(int i = 0; i < XOROSHIRO128_UNROLL; i++) s[1][i] = rotl(s1[i], 37);
	}

	// This is just to avoid dead-code elimination
	return array[0] ^ array[len - 1];
}

#define INIT for(int i = 0; i < XOROSHIRO128_UNROLL; i++) s[0][i] = 1 << i;
#define NEXT next(result, 1000);
#include "harness.c"
