<?php
// P<PERSON> redirects from the original file, ensure they are at the very top
if (preg_match("/pcg.di.unimi.it/", $_SERVER["HTTP_HOST"], $m)) {
    Header("HTTP/1.1 302 Found");
    Header("Location: https://pcg.di.unimi.it/pcg.php");
    exit;
}

if (preg_match("/(random|pseudorandom|xorshift|xoroshiro|xoshiro).di.unimi.it/", $_SERVER["HTTP_HOST"], $m)) {
    Header("HTTP/1.1 302 Found");
    Header("Location: https://prng.di.unimi.it" . $_SERVER["REQUEST_URI"]);
    exit;
}

// Function to determine if a given path matches the current page, for active link highlighting
// (Modified for a single-page site with internal anchors)
function isActiveAnchor($anchor_id)
{
    if (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '#' . $anchor_id) !== false) {
        return 'active';
    }
    return '';
}

// Define the navigation items for this page's sections
// These correspond to the IDs of the H1/H2 tags on the page
$prngNavItems = [
    ['label' => 'Testing Hamming–Weight Dependencies', 'id' => 'intro'],
    ['label' => 'Results', 'id' => 'results'],
    ['label' => 'Running', 'id' => 'running'],
];
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="keywords"
        content="rng, prng, xoshiro, xoroshiro, xorshift, pseudorandom number generator, random number generator">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>Testing Hamming–Weight Dependencies</title>

    <link href="https://fonts.googleapis.com/css2?family=Hack&family=Roboto+Mono:wght@400;700&display=swap"
        rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
    <link href="style.css" rel="stylesheet">
</head>

<body class="bg-dark text-light">

    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top shadow-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="https://prng.di.unimi.it/">
                <i class="bi bi-cpu-fill me-2"></i>A PRNG Shootout
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#prngNavbar"
                aria-controls="prngNavbar" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="prngNavbar">
                <ul class="navbar-nav ms-auto">
                    <?php foreach ($prngNavItems as $item): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActiveAnchor($item['id']); ?>"
                                href="#<?php echo $item['id']; ?>">
                                <?php echo $item['label']; ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </nav>

    <main class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-9">
                <div class="row">

                    <div class="col-lg-9">
                        <h1 class="first" id="intro">Testing Hamming–Weight Dependencies</h1>

                        <p>This page reports results of our new <a
                                href="https://vigna.di.unimi.it/papers.php#BlVNTHWD">test for Hamming-weight
                                dependencies</a>. It is a very strong
                            test, looking for dependencies between the number of zeroes and ones in consecutive
                            outputs, and it is engineered to the point that it can be practically run on petabytes
                            of data (given that the generator is fast enough).

                        <p>Besides find bias in our own generators, such as <code>xorshift128+</code>,
                            we were able to find some new, unknown bias in previous generators such as some versions of
                            the <a href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/emt.html">Mersenne
                                Twister</a>,
                            of the <a href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/TINYMT/index.html">Tiny
                                Mersenne Twister</a>,
                            and of the <a href="https://https://dl.acm.org/doi/10.1145/1132973.1132974">WELL</a> family,
                            for which <em>no tests other than linearity were known to fail</em>.
                            Note that <code>xorshift128+</code>
                            fails the test after 6 GB of output, but the SIMD-oriented Fast Mersenne Twister (607 bits)
                            fails after much less output. <code>xoroshiro128+</code> needs four
                            orders of magnitude more data, and <a href="index.php">the other generators we propose</a>
                            show no sign of bias after a
                            petabyte (10<sup>15</sup> bytes) of output.



                        <h1 id="results">Results</h1>

                        <p>To understand fully the columns of this table, we suggest to have a look at the
                            description of the test in the paper. The third column shows the amount of output that has
                            to
                            be processed to obtain a <var>p</var>-value below 10<sup>-20</sup>. Note that the
                            Mersenne Twister sports multiple values, as we tested multiple possible parameters
                            using the <a href="https://github.com/MersenneTwister-Lab/dcmt">dcmt library</a>.
                            Analogously,
                            we tested several parameters of the Tiny Mersenne Twister.

                        <div class="table-responsive">
                            <table id='test-table' class='table table-striped table-hover table-bordered caption-top'>
                                <caption>HWD Results</caption>
                                <thead>
                                    <tr>
                                        <th>PRNG
                                        <th><var>w</var>
                                        <th>Period
                                        <th><var>p</var> = 10<sup>&minus;20</sup> @
                                        <th>Faulty Signature
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><a href="xorshift128plus.c"><code>xorshift128+</code></a>
                                        <td>64
                                        <td data-order="128">2<sup>128</sup>&nbsp;&minus;&nbsp;1
                                        <td data-order="9">6 &times; 10<sup>9</sup>
                                        <td>00000021
                                    <tr>
                                        <td><a href="xoroshiro128plus.c"><code>xoroshiro128+</code></a>
                                        <td>64
                                        <td data-order="128">2<sup>128</sup>&nbsp;&minus;&nbsp;1
                                        <td data-order="12.5">5 &times; 10<sup>12</sup>
                                        <td>00000012
                                    <tr>
                                        <td><a
                                                href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/TINYMT/index.html">Tiny
                                                Mersenne Twister</a> (64 bits)
                                        <td>32
                                        <td data-order="127">2<sup>127</sup>&nbsp;&minus;&nbsp;1
                                        <td data-order="13.8">8 &times; 10<sup>13</sup>→
                                        <td>10001021
                                    <tr>
                                        <td><a
                                                href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/TINYMT/index.html">Tiny
                                                Mersenne Twister</a> (32 bits)
                                        <td>32
                                        <td data-order="127">2<sup>127</sup>&nbsp;&minus;&nbsp;1
                                        <td data-order="13.4">4 &times; 10<sup>13</sup>→
                                        <td>10001021
                                    <tr>
                                        <td><a href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/SFMT/">SFMT</a>
                                            (607 bits)
                                        <td>32
                                        <td data-order="607">2<sup>607</sup>&nbsp;&minus;&nbsp;1
                                        <td data-order="8">4 &times; 10<sup>8</sup>
                                        <td>001000001000
                                    <tr>
                                        <td><a href="https://github.com/MersenneTwister-Lab/dSFMT">dSFMT</a> (521 bits)
                                        <td>32
                                        <td data-order="521">2<sup>521</sup>&nbsp;&minus;&nbsp;1
                                        <td data-order="12.6">6 &times; 10<sup>12</sup>
                                        <td>1001000100100010
                                    <tr>
                                        <td><a href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/emt.html">Mersenne
                                                Twister</a> (521 bits)
                                        <td>32
                                        <td data-order="521">2<sup>521</sup>&nbsp;&minus;&nbsp;1
                                        <td data-order="10">4 &times; 10<sup>10</sup> →
                                        <td>1000000100000000, 2000000100000000
                                    <tr>
                                        <td><a href="https://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/emt.html">Mersenne
                                                Twister</a> (607 bits)
                                        <td>32
                                        <td data-order="607">2<sup>607</sup>&nbsp;&minus;&nbsp;1
                                        <td data-order="8">4 &times; 10<sup>8</sup> → 4 &times; 10<sup>10</sup>
                                        <td>1000000001000000000, 2000000001000000000
                                    <tr>
                                        <td><a
                                                href="https://dl.acm.org/doi/10.1145/1132973.1132974">WELL512a</a>
                                            (512 bits)
                                        <td>32
                                        <td data-order="512">2<sup>512</sup>&nbsp;&minus;&nbsp;1
                                        <td data-order="15">3 &times; 10<sup>15</sup>
                                        <td>2001002200000000
                                </tbody>
                            </table>
                        </div>



                        <h1 id="running">Running</h1>

                        <p>To run the test on your own, please download the <a href="hwd.c">source code</a>, whose
                            comments contain
                            compilation instructions. You <em>must</em> embed your generator in the code&mdash;there
                            is no other practical way of testing in the petabyte range. You just have to modify the <a
                                href="prngs_hwd.c"><code>prngs_hwd.c</code></a>
                            file to implement the <code>next()</code> function of your generator.

                    </div>

                    <div class="col-lg-3">
                        <aside class="pt-lg-4">


                            <h5 class="mt-4 mt-lg-0">C code (64 bits)</h5>
                            <p>
                            <ul>
                                <li><a HREF="hwd.c"><code>hwd.c</code></a>
                                <li><a HREF="prngs_hwd.c"><code>prngs_hwd.c</code></a>
                            </ul>

                            <h5>Documentation</h5>
                            <p>
                            <ul>
                                <li>The <a href="https://vigna.di.unimi.it/papers.php#BlVNTHWD">paper</a> discussing the
                                    Hamming-weight dependency test.
                            </ul>

                            <h5>Validation</h5>
                            <p><a href="https://validator.w3.org/check?uri=<?php echo urlencode($_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>"
                                    class="text-decoration-none">This is valid HTML5</a></p>
                        </aside>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-dark text-center py-3 mt-5 border-top border-secondary">
        <div class="container">
            <div class="row">
                <div class="col-12 text-light">
                    <p class="mb-0">&copy; <?php echo date("Y"); ?> Sebastiano Vigna. All rights reserved.</p>
                    <p class="mb-0">Built with <a href="https://getbootstrap.com/"
                            class="text-info text-decoration-none" target="_blank"
                            rel="noopener noreferrer">Bootstrap</a> and
                        <a href="https://php.net/" class="text-info text-decoration-none" target="_blank"
                            rel="noopener noreferrer">PHP</a>.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
        crossorigin="anonymous"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>

    <script>
        $(document).ready(function () {
            $('#test-table').DataTable({
                // Optional: Customize DataTables here
                paging: false,      // Disable pagination
                searching: false,   // Disable search box
                info: false,        // Disable "Showing X of Y entries" info
                ordering: true,     // Enable ordering (sorting)
                order: [[0, 'asc']], // Default sort by first column ascending
                columnDefs: [
                    { type: 'num', targets: [1] } // Treat column 1 as numbers for sorting
                ]
            });

            var $sections = $('h1[id]'); // Target h1 elements with an ID
            var $navLinks = $('.navbar-nav .nav-link');
            var navbarHeight = $('.navbar.sticky-top').outerHeight(); // Get the actual navbar height

            // Function to update active link on scroll
            function updateActiveNavLink() {
                lastId = "intro";
                $sections.each(function () {
                    var sectionTop = $(this).offset().top - navbarHeight - 10; // Adjust for navbar height + a small buffer

                    if (sectionTop < 0) {
                        lastId = $(this).attr('id');
                    }
                });

                $navLinks.removeClass('active');
                $('a.nav-link[href="#' + lastId + '"]').addClass('active');
            }

            // Run on scroll
            document.body.addEventListener('scroll', updateActiveNavLink);

            // Enhance existing smooth scroll for internal links to immediately set active class
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    $navLinks.removeClass('active'); // Remove active from all
                    $(this).addClass('active');      // Add active to the clicked one
                });
            });

            // Initial check on page load to set the correct active link based on URL hash
            updateActiveNavLink();

            // Smooth scroll for internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1); // Remove '#'
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }); // End of $(document).ready
    </script>