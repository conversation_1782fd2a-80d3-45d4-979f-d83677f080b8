Intel(R) Architecture Code Analyzer Version -  v3.0-28-g1ba2cbb build date: 2017-10-30;16:57:45
Analyzed File -  ./pcg64dxsm-iaca
Binary Format - 64Bit
Architecture  -  SKL
Analysis Type - Throughput

Throughput Analysis Report
--------------------------
Block Throughput: 5.53 Cycles       Throughput Bottleneck: FrontEnd
Loop Count:  22
Port Binding In Cycles Per Iteration:
--------------------------------------------------------------------------------------------------
|  Port  |   0   -  DV   |   1   |   2   -  D    |   3   -  D    |   4   |   5   |   6   |   7   |
--------------------------------------------------------------------------------------------------
| Cycles |  3.6     0.0  |  4.0  |  1.3     1.0  |  1.4     1.0  |  2.0  |  3.7  |  3.7  |  1.3  |
--------------------------------------------------------------------------------------------------

DV - Divider pipe (on port 0)
D - Data fetch pipe (on ports 2 and 3)
F - Macro Fusion with the previous instruction occurred
* - instruction micro-ops not bound to a port
^ - Micro Fusion occurred
# - ESP Tracking sync uop was issued
@ - SSE instruction followed an AVX256/AVX512 instruction, dozens of cycles penalty is expected
X - instruction not supported, was not accounted in Analysis

| Num Of   |                    Ports pressure in cycles                         |      |
|  Uops    |  0  - DV    |  1   |  2  -  D    |  3  -  D    |  4   |  5   |  6   |  7   |
-----------------------------------------------------------------------------------------
|   1      |             |      |             |             |      | 1.0  |      |      | lea r8, ptr [rip+0x18d]
|   1      |             |      | 0.6     0.6 | 0.4     0.4 |      |      |      |      | mov rdx, qword ptr [r8]
|   1      |             |      | 0.4     0.4 | 0.6     0.6 |      |      |      |      | mov rcx, qword ptr [r8+0x8]
|   1*     |             |      |             |             |      |      |      |      | mov rdi, rdx
|   1      | 0.6         |      |             |             |      |      | 0.4  |      | or rdi, 0x1
|   1*     |             |      |             |             |      |      |      |      | mov rsi, rcx
|   1      | 0.4         |      |             |             |      |      | 0.6  |      | shr rsi, 0x20
|   1      | 0.3         |      |             |             |      | 0.7  |      |      | xor rsi, rcx
|   1      | 0.3         |      |             |             |      | 0.3  | 0.4  |      | mov r9, 0xda942042e4dd58b5
|   1      |             | 1.0  |             |             |      |      |      |      | imul rsi, r9
|   1*     |             |      |             |             |      |      |      |      | mov rax, rsi
|   1      | 0.4         |      |             |             |      |      | 0.6  |      | shr rax, 0x30
|   1      | 0.3         |      |             |             |      | 0.7  |      |      | xor rax, rsi
|   1      |             | 1.0  |             |             |      |      |      |      | imul rax, rdi
|   1      |             | 1.0  |             |             |      |      |      |      | imul rcx, r9
|   2      |             | 1.0  |             |             |      | 1.0  |      |      | mulx rsi, rdx, r9
|   1      | 0.3         |      |             |             |      |      | 0.7  |      | mov rdi, 0xdeadbeefdeadf00d
|   1      | 0.7         |      |             |             |      |      | 0.3  |      | add rdi, rdx
|   1      | 0.3         |      |             |             |      |      | 0.7  |      | adc rsi, rcx
|   2^     |             |      |             |             | 1.0  |      |      | 1.0  | mov qword ptr [r8], rdi
|   2^     |             |      | 0.3         | 0.4         | 1.0  |      |      | 0.3  | mov qword ptr [r8+0x8], rsi
Total Num Of Uops: 24
