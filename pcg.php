<?php

if (preg_match("/(random|pseudorandom|xorshift|xoroshiro|xoshiro).di.unimi.it/", $_SERVER["HTTP_HOST"], $m)) {
    Header("HTTP/1.1 302 Found");
    Header("Location: https://prng.di.unimi.it" . $_SERVER["REQUEST_URI"]);
    exit;
}

// Function to determine if a given path matches the current page, for active link highlighting
// (Modified for a single-page site with internal anchors)
function isActiveAnchor($anchor_id)
{
    if (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '#' . $anchor_id) !== false) {
        return 'active';
    }
    return '';
}

// Define the navigation items for this page's sections
// These correspond to the IDs of the H1/H2 tags on the page
$prngNavItems = [
    ['label' => 'The Wrap–Up on PCG Generators', 'id' => 'wrap-up'],
    ['label' => 'Statistical Flaws', 'id' => 'flaws'],
    ['label' => 'False Claims', 'id' => 'claims'],
    ['label' => 'Conclusions', 'id' => 'conclusions']
];
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="keywords"
        content="rng, prng, xoshiro, xoroshiro, xorshift, pseudorandom number generator, random number generator">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>The Wrap–Up on PCG Generators</title>

    <link href="https://fonts.googleapis.com/css2?family=Hack&family=Roboto+Mono:wght@400;700&display=swap"
        rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
    <link href="style.css" rel="stylesheet">
</head>

<body class="bg-dark text-light">

    <nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top shadow-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="https://prng.di.unimi.it/">
                <i class="bi bi-cpu-fill me-2"></i> PRNG Shootout
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#prngNavbar"
                aria-controls="prngNavbar" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="prngNavbar">
                <ul class="navbar-nav ms-auto">
                    <?php foreach ($prngNavItems as $item): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActiveAnchor($item['id']); ?>"
                                href="#<?php echo $item['id']; ?>">
                                <?php echo $item['label']; ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </nav>

    <main class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-9">
                <div class="row">

                    <div class="col-lg-9">

                        <h1 class=first id="wrap-up">The Wrap–Up on PCG Generators</h1>

                        <p>This page reviews <a href="https://www.pcg-random.org/">Melissa O'Neill's PCG generators</a>.
                            It is structured as follows: first, we identify the
                            generators with major statistical flaw. Once those are out of the picture, we examine some
                            claims made by
                            Melissa O'Neill on the remaining PCG generators, and provide simple counterexamples for each
                            claim. In the
                            end, we show simple generators based on congruential arithmetic that are faster and have
                            better statistical properties
                            of the only remaining 64-bit PCG generators.

                        <p>The conclusion is that there is no reason to use PCG generators: for those without
                            statistical flaws, there is always a better choice.

                        <h1 id="flaws">Statistical Flaws</h1>

                        <h2>Generator With &ldquo;ext&rdquo; in the Name</h2>

                        <p>Generator with a large state space are useful because they generate many distinct and
                            uncorrelated
                            sequences. In theory, all nonoverlapping subsequences should look
                            random, so if you take <var>R</var> and <var>R</var>' initialized in a different state and
                            interleave their output (first output from <var>R</var>, then first output from
                            <var>R</var>',
                            then second output from <var>R</var>, etc.) the resulting sequence should still look random.

                        <p>But nothing is really random. So what you do is correlation testing: you
                            take <var>R</var>. Then you take an exact copy of <var>R</var>, <var>R</var>', and you flip
                            exactly one bit
                            of the state. Then you interleave as above, and examine the output
                            (usually using some test suite, you can choose the one you prefer).

                        <p>In theory the interleaved output should immediately look random, but that's very
                            difficult to obtain, because it takes time for the flipped bit to
                            influence all the state. So you have to throw away some output from
                            <var>R</var>, <var>R</var>' before they <em>decorrelate</em>.
                            For example, <code>xoroshiro128+</code> decorrelates after
                            a couple of dozens iterations. The Mersenne Twister (19937 bits of state) after millions of
                            iterations. CMWC4096, the &ldquo;Mother-of-all&rdquo; PRNG by Marsaglia, needs
                            more than 10,000,000 iterations. That is, you have to discard 10,000,000 values from
                            <var>R</var> and <var>R</var>' before you see a random interleaved stream.

                        <p>LCG generators can only be as large as the largest multiplications you can perform. Melissa
                            O'Neill devised a new
                            type of generators (the ones with &ldquo;ext&rdquo; in their name) which, mysteriously,
                            overcome this limit.

                        <p>So let us try to see how much does it take for an &ldquo;ext&rdquo; PCG generator to
                            decorrelate. I wrote
                            a <a href="uncpcg.cpp">C++ program</a> that does a correlation test for you: it creates
                            two &ldquo;ext&rdquo; PCG generators, flips a bit of the &ldquo;ext&rdquo; state, and
                            emits the output interleaved. It takes a parameter that specifies the number of iterations
                            to perform before emitting the interleaved stream,
                            so we can measure whether, for example, uncorrelation is faster than with the Mersenne
                            Twister.

                        <p>This is the result with 1 million discarded outputs:

                        <pre>
        rng=RNG_stdin64, seed=0x5e3432a3
        length= 256 megabytes (2^28 bytes), time= 2.2 seconds
          Test Name                         Raw       Processed     Evaluation
          BCFN(2+0,13-2,T)                  R=+5218351 p = 0           FAIL !!!!!!!!
          BCFN(2+1,13-2,T)                  R=+3553737 p = 0           FAIL !!!!!!!!
          BCFN(2+2,13-3,T)                  R= +4207  p =  3e-1991    FAIL !!!!!!!!
          BCFN(2+3,13-3,T)                  R=+966.0  p =  4.9e-457   FAIL !!!!!!!
          BCFN(2+4,13-3,T)                  R=+203.5  p =  4.2e-96    FAIL !!!!!
          BCFN(2+5,13-4,T)                  R= +30.7  p =  2.2e-13    FAIL
          DC6-9x1Bytes-1                    R=+313150 p = 0           FAIL !!!!!!!!
          Gap-16:A                          R=+2924020 p = 0           FAIL !!!!!!!!
          Gap-16:B                          R=+11092511 p = 0           FAIL !!!!!!!!
          FPF-14+6/16:(0,14-0)              R=+121.6  p =  3.6e-112   FAIL !!!!!
          FPF-14+6/16:(1,14-0)              R=+125.2  p =  1.8e-115   FAIL !!!!!
          FPF-14+6/16:(2,14-0)              R=+109.4  p =  7.2e-101   FAIL !!!!!
          FPF-14+6/16:(3,14-0)              R= +99.7  p =  7.5e-92    FAIL !!!!!
          FPF-14+6/16:(4,14-1)              R= +72.7  p =  3.4e-64    FAIL !!!!
          FPF-14+6/16:(5,14-2)              R= +55.4  p =  2.9e-48    FAIL !!!
          FPF-14+6/16:(6,14-2)              R= +48.4  p =  4.2e-42    FAIL !!!
          FPF-14+6/16:(7,14-3)              R= +37.3  p =  1.7e-32    FAIL !!!
          FPF-14+6/16:(8,14-4)              R= +22.5  p =  2.5e-18    FAIL !
          FPF-14+6/16:(9,14-5)              R= +22.2  p =  3.0e-18    FAIL !
          FPF-14+6/16:(10,14-5)             R= +18.2  p =  6.2e-15    FAIL
          FPF-14+6/16:(13,14-8)             R=  +8.6  p =  3.0e-6   unusual
          FPF-14+6/16:(15,14-9)             R=  +8.7  p =  8.9e-6   unusual
          FPF-14+6/16:all                   R=+255.7  p =  1.7e-239   FAIL !!!!!!
          FPF-14+6/16:all2                  R=+14532  p =  4e-5671    FAIL !!!!!!!!
          BRank(12):128(4)                  R= +2501  p~=  3e-1331    FAIL !!!!!!!!
          BRank(12):256(4)                  R= +5257  p~=  1e-2796    FAIL !!!!!!!!
          BRank(12):384(1)                  R= +3963  p~=  4e-1194    FAIL !!!!!!!!
          BRank(12):512(2)                  R= +7614  p~=  4e-2293    FAIL !!!!!!!!
          BRank(12):768(1)                  R= +8096  p~=  2e-2438    FAIL !!!!!!!!
          BRank(12):1K(2)                   R=+15408  p~=  3e-4639    FAIL !!!!!!!!
          BRank(12):1536(1)                 R=+16298  p~=  2e-4907    FAIL !!!!!!!!
          BRank(12):2K(1)                   R=+21895  p~=  3e-6592    FAIL !!!!!!!!
          [Low16/64]BCFN(2+0,13-3,T)        R=+16181  p =  2e-7658    FAIL !!!!!!!!
          [Low16/64]BCFN(2+1,13-3,T)        R= +3654  p =  3e-1729    FAIL !!!!!!!!
          [Low16/64]BCFN(2+2,13-4,T)        R=+903.0  p =  1.8e-394   FAIL !!!!!!!
          [Low16/64]BCFN(2+3,13-4,T)        R=+118.4  p =  1.1e-51    FAIL !!!!
          [Low16/64]BCFN(2+5,13-5,T)        R= +13.7  p =  1.5e-5   mildly suspicious
          [Low16/64]DC6-9x1Bytes-1          R=+965794 p = 0           FAIL !!!!!!!!
          [Low16/64]Gap-16:A                R=+838770 p = 0           FAIL !!!!!!!!
          [Low16/64]Gap-16:B                R=+3576303 p = 0           FAIL !!!!!!!!
          [Low16/64]FPF-14+6/16:(0,14-0)    R=+122.4  p =  7.1e-113   FAIL !!!!!
          [Low16/64]FPF-14+6/16:(1,14-0)    R=+127.5  p =  1.3e-117   FAIL !!!!!
          [Low16/64]FPF-14+6/16:(2,14-1)    R= +85.7  p =  1.0e-75    FAIL !!!!
          [Low16/64]FPF-14+6/16:(3,14-2)    R= +59.7  p =  5.4e-52    FAIL !!!!
          [Low16/64]FPF-14+6/16:(4,14-2)    R= +4010  p =  4e-3507    FAIL !!!!!!!!
          [Low16/64]FPF-14+6/16:(5,14-3)    R= +2834  p =  4e-2484    FAIL !!!!!!!!
          [Low16/64]FPF-14+6/16:(6,14-4)    R= +2010  p =  2e-1642    FAIL !!!!!!!!
          [Low16/64]FPF-14+6/16:(7,14-5)    R= +1428  p =  1e-1183    FAIL !!!!!!!!
          [Low16/64]FPF-14+6/16:(8,14-5)    R= +1714  p =  2e-1421    FAIL !!!!!!!!
          [Low16/64]FPF-14+6/16:(9,14-6)    R= +1216  p =  2.1e-930   FAIL !!!!!!!
          [Low16/64]FPF-14+6/16:(10,14-7)   R=+849.5  p =  6.3e-676   FAIL !!!!!!!
          [Low16/64]FPF-14+6/16:(11,14-8)   R=+584.7  p =  8.6e-421   FAIL !!!!!!!
          [Low16/64]FPF-14+6/16:(12,14-8)   R=+540.8  p =  3.4e-389   FAIL !!!!!!!
          [Low16/64]FPF-14+6/16:(13,14-9)   R=+384.8  p =  1.4e-242   FAIL !!!!!!
          [Low16/64]FPF-14+6/16:(14,14-10)  R=+269.5  p =  6.3e-144   FAIL !!!!!
          [Low16/64]FPF-14+6/16:(15,14-11)  R=+214.6  p =  2.2e-94    FAIL !!!!!
          [Low16/64]FPF-14+6/16:all         R= +2608  p =  1e-2447    FAIL !!!!!!!!
          [Low16/64]FPF-14+6/16:all2        R=+7572279 p = 0           FAIL !!!!!!!!
          [Low16/64]FPF-14+6/16:cross       R= +75.0  p =  2.8e-66    FAIL !!!!
          [Low16/64]BRank(12):128(4)        R= +2501  p~=  3e-1331    FAIL !!!!!!!!
          [Low16/64]BRank(12):256(2)        R= +3717  p~=  5e-1120    FAIL !!!!!!!!
          [Low16/64]BRank(12):384(1)        R= +3963  p~=  4e-1194    FAIL !!!!!!!!
          [Low16/64]BRank(12):512(2)        R= +7599  p~=  1e-2288    FAIL !!!!!!!!
          [Low16/64]BRank(12):768(1)        R= +8096  p~=  2e-2438    FAIL !!!!!!!!
          [Low16/64]BRank(12):1K(1)         R=+10873  p~=  3e-3274    FAIL !!!!!!!!
          [Low4/64]BCFN(2+0,13-5,T)         R= +4244  p =  1e-1661    FAIL !!!!!!!!
          [Low4/64]BCFN(2+1,13-5,T)         R=+597.8  p =  3.4e-234   FAIL !!!!!!
          [Low4/64]BCFN(2+2,13-5,T)         R= +13.7  p =  1.5e-5   suspicious
          [Low4/64]BCFN(2+3,13-5,T)         R= +61.2  p =  3.7e-24    FAIL !!
          [Low4/64]BCFN(2+4,13-6,T)         R= +41.7  p =  1.0e-14    FAIL
          [Low4/64]BCFN(2+5,13-6,T)         R= +14.0  p =  3.0e-5   unusual
          [Low4/64]DC6-9x1Bytes-1           R=+115116 p = 0           FAIL !!!!!!!!
          [Low4/64]Gap-16:A                 R=+247785 p = 0           FAIL !!!!!!!!
          [Low4/64]Gap-16:B                 R=+1522079 p = 0           FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:(0,14-1)     R=+363333 p = 0           FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:(1,14-2)     R=+256697 p = 0           FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:(2,14-2)     R=+148798 p = 0           FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:(3,14-3)     R=+105460 p = 0           FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:(8,14-7)     R=+111527 p = 0           FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:(9,14-8)     R=+78894  p = 0           FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:(10,14-8)    R=+39682  p = 0           FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:(11,14-9)    R=+28140  p = 0           FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:all          R=+502730 p = 0           FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:all2         R=+68561676162 p = 0           FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:cross        R=+525391 p = 0           FAIL !!!!!!!!
          [Low4/64]BRank(12):128(4)         R= +2501  p~=  3e-1331    FAIL !!!!!!!!
          [Low4/64]BRank(12):256(2)         R= +3717  p~=  5e-1120    FAIL !!!!!!!!
          [Low4/64]BRank(12):384(1)         R= +4028  p~=  1e-1213    FAIL !!!!!!!!
          [Low4/64]BRank(12):512(2)         R= +7599  p~=  1e-2288    FAIL !!!!!!!!
          [Low4/64]BRank(12):768(1)         R= +8096  p~=  2e-2438    FAIL !!!!!!!!
          [Low1/64]BCFN(2+0,13-6,T)         R= +76.9  p =  8.8e-27    FAIL !!
          [Low1/64]BCFN(2+1,13-6,T)         R=+375.0  p =  7.6e-129   FAIL !!!!!
          [Low1/64]BCFN(2+2,13-6,T)         R=+167.7  p =  7.4e-58    FAIL !!!!
          [Low1/64]BCFN(2+3,13-6,T)         R= +42.5  p =  5.5e-15    FAIL !
          [Low1/64]BCFN(2+4,13-7,T)         R= +16.2  p =  1.6e-5   mildly suspicious
          [Low1/64]DC6-9x1Bytes-1           R=+36425  p = 0           FAIL !!!!!!!!
          [Low1/64]Gap-16:A                 R=+88655  p = 0           FAIL !!!!!!!!
          [Low1/64]Gap-16:B                 R=+526412 p = 0           FAIL !!!!!!!!
          [Low1/64]FPF-14+6/16:(0,14-2)     R=+123481 p = 0           FAIL !!!!!!!!
          [Low1/64]FPF-14+6/16:(2,14-4)     R=+100738 p = 0           FAIL !!!!!!!!
          [Low1/64]FPF-14+6/16:(4,14-5)     R=+71077  p = 0           FAIL !!!!!!!!
          [Low1/64]FPF-14+6/16:(6,14-7)     R=+55336  p = 0           FAIL !!!!!!!!
          [Low1/64]FPF-14+6/16:(8,14-8)     R=+27843  p = 0           FAIL !!!!!!!!
          [Low1/64]FPF-14+6/16:(10,14-10)   R=+16804  p =  2e-8940    FAIL !!!!!!!!
          [Low1/64]FPF-14+6/16:(11,14-11)   R= +2225  p =  2.0e-970   FAIL !!!!!!!
          [Low1/64]FPF-14+6/16:(12,14-11)   R=+14981  p =  2e-6530    FAIL !!!!!!!!
          [Low1/64]FPF-14+6/16:all          R=+215351 p = 0           FAIL !!!!!!!!
          [Low1/64]FPF-14+6/16:all2         R=+10216812555 p = 0           FAIL !!!!!!!!
          [Low1/64]FPF-14+6/16:cross        R=+521082 p = 0           FAIL !!!!!!!!
          [Low1/64]BRank(12):128(2)         R= +1769  p~=  1.8e-533   FAIL !!!!!!!
          [Low1/64]BRank(12):256(2)         R= +3717  p~=  5e-1120    FAIL !!!!!!!!
          [Low1/64]BRank(12):384(1)         R= +4006  p~=  5e-1207    FAIL !!!!!!!!
          [Low1/64]BRank(12):512(1)         R= +5362  p~=  2e-1615    FAIL !!!!!!!!
          ...and 37 test result(s) without anomalies
</pre>

                        <p>This does not look good. And with 1 <em>billion</em> discarded outputs the result is the same
                            (try yourself). What's happening?

                        <p>The problem is that &ldquo;ext&rdquo; PCG generators <em>never</em> decorrelate.
                            <em>Ever</em>
                            (&ldquo;never&rdquo; here means &ldquo;not before the thermodynamic
                            death of the universe&rdquo;). There is no state mix.
                            Nobody has ever thought of designing a PRNG in such a flawed way.

                        <p>The trick used by Melissa O'Neill to make people believe she could have high-quality,
                            large-state generators
                            using LCGs consists, in practice, in xoring the output of a small (at most 128 bits of
                            state) PCG generator
                            with a large array. The array in theory changes, and waiting enough (much beyond the
                            thermodynamical death of the universe)
                            you will see it pass all states, but in practice the array never changes. This is why we
                            cannot decorrelate: the
                            output of <var>R</var> and <var>R</var>' <em>is essentially the same, no matter how much you
                                look for uncorrelated sequences</em>.

                        <p>Said otherwise, the whole sequence of the generator is made by an enormous number of strongly
                            correlated, very short
                            sequences. And this makes the correlation tests fail.

                        <p>You can see this easily by uncommenting the <code>printf()</code> calls in the code and
                            commenting the <code>fwrite()</code> calls.
                            After a billion iterations, we obtain

                        <pre>
        d355d4a2d198b55d
        d355d4a2d198b55d
        943c19906dee85e4
        943c19906dee85e4
        0d4a2d01a24d80ae
        0d4a2d01a24d80ae
        12d5e10f7a626cbd
        12d5e10f7a626cbd
        15aedbb162473964
        15aedbb162473964
        a9700b058d3d2619
        a9700b058d3d2619
        6a5ed46c771c73d2
        6a5ed46c771c73d2
        426ca5c99a4980d1
        426ca5c99a4980d1
        b793262f0f13b965
        b793262f0f13b965
        be6724c0f4789316
        be6724c0f4789316
        2ee214efcc33da12
        2ee214efcc33da12
        38f221757282c60e
        38f221757282c60e
        e03e6c696146fc81
        e03e6c696146fc81
        16b1ec780e875744
        8f9efedf6709a41e
        fe8dbdbbf39eddd3
        fe8dbdbbf39eddd3
        047ff5f2784f6e08
        047ff5f2784f6e08
        6d91e2bebd70954a
        6d91e2bebd70954a
</pre>
                        <p>Almost all outputs are duplicate: the two generators are not decorrelating. If you look
                            carefully, you'll find two values that
                            are not duplicate. This is all the decorrelation we get after a billion iterations, and it
                            will not improve (not significantly
                            before the thermodynamical death of the universe).

                        <p>Of course, the
                            same test on the very small number of bits of the base generator should work without
                            problems (but read below):
                            nonetheless, the test fails for
                            the large majority of bits of state; and the larger the state space, the worst the
                            percentage of failing bits. No
                            modern generator will fail this test on a majority of bits; in fact, good generators do not
                            fail it on <em>any</em> bit.
                            You just might have to discard a small amount of output values.

                        <p>Wrap-up: <strong>do not use PCG generators with &ldquo;ext&rdquo; in the name</strong>.


                        <h2>Generators with Multiple Sequences</h2>

                        <p>Melissa O'Neill claims that a strong point of PCG generators is the
                            possibility of generating multiple independent streams by changing the
                            additive constant of the underlying LCG generator. This fact is stated
                            without any proof, and indeed it is entirely false. You can definitely
                            generate multiple streams, but they might end up being extremely
                            correlated. This is known at least since Knuth's description of LCGs in
                            TAoCP (like, half a century?) because it is possible to derive easily
                            the sequence for a constant given the sequence for <em>another</em> constant.
                            That is, the sequences are strongly correlated, and the minimal scrambling
                            performed by PCG generator is absolutely insufficient to hide this correlation.

                        <p>To check that this actually happens, I put together a small <a href="corrpcg.c">C program</a>
                            which creates two PCG generators with seemingly random initial states
                        <pre>
        0x7C112EEA363433CFB3AA1BA7C748A9B9
        0x83EED115C9CBCC304C55E45838B75647
</pre>
                        <p>and seemingly random increments
                        <pre>
        0x3E0897751B1A19E7D9D50DD3E3A454DC
        0x41F7688AE4E5E618262AF22C1C5BAB23
</pre>

                        <p>Following Melissa O'Neill claims, if we interleave the output of these two generators we
                            should
                            see a random stream. But if you pipe the output of the program into PractRand this is what
                            you will see:

                        <pre>
        rng=RNG_stdin64, seed=0x66b6c6c9
        length= 256 megabytes (2^28 bytes), time= 2.9 seconds
          Test Name                         Raw       Processed     Evaluation
          BCFN(2+0,13-2,T)                  R=+401986 p = 0           FAIL !!!!!!!!
          BCFN(2+1,13-2,T)                  R=+188.0  p =  1.1e-95    FAIL !!!!!
          DC6-9x1Bytes-1                    R= +7342  p =  1e-3850    FAIL !!!!!!!!
          [Low16/64]BCFN(2+0,13-3,T)        R= +3288  p =  6e-1556    FAIL !!!!!!!!
          [Low16/64]BCFN(2+1,13-3,T)        R=+841.7  p =  3.6e-398   FAIL !!!!!!!
          [Low16/64]BCFN(2+2,13-4,T)        R=+266.1  p =  3.2e-116   FAIL !!!!!
          [Low16/64]BCFN(2+3,13-4,T)        R= +61.8  p =  6.3e-27    FAIL !!
          [Low16/64]BCFN(2+4,13-5,T)        R= +14.4  p =  8.1e-6   mildly suspicious
          [Low16/64]DC6-9x1Bytes-1          R=+30611  p = 0           FAIL !!!!!!!!
          [Low16/64]FPF-14+6/16:(4,14-2)    R=+162.5  p =  6.8e-142   FAIL !!!!!
          [Low16/64]FPF-14+6/16:(5,14-3)    R= +69.8  p =  6.7e-61    FAIL !!!!
          [Low16/64]FPF-14+6/16:(6,14-4)    R=+112.0  p =  1.9e-91    FAIL !!!!!
          [Low16/64]FPF-14+6/16:(7,14-5)    R= +43.2  p =  1.1e-35    FAIL !!!
          [Low16/64]FPF-14+6/16:(8,14-5)    R=+106.9  p =  1.7e-88    FAIL !!!!
          [Low16/64]FPF-14+6/16:(9,14-6)    R=+118.2  p =  1.6e-90    FAIL !!!!!
          [Low16/64]FPF-14+6/16:(10,14-7)   R= +78.3  p =  3.9e-62    FAIL !!!!
          [Low16/64]FPF-14+6/16:(11,14-8)   R= +82.3  p =  3.0e-59    FAIL !!!!
          [Low16/64]FPF-14+6/16:(12,14-8)   R= +81.6  p =  9.5e-59    FAIL !!!!
          [Low16/64]FPF-14+6/16:(13,14-9)   R= +57.3  p =  2.3e-36    FAIL !!!
          [Low16/64]FPF-14+6/16:(14,14-10)  R= +50.0  p =  3.6e-27    FAIL !!
          [Low16/64]FPF-14+6/16:(15,14-11)  R= +21.8  p =  2.4e-10  very suspicious
          [Low16/64]FPF-14+6/16:all         R=+106.3  p =  3.1e-99    FAIL !!!!!
          [Low16/64]FPF-14+6/16:all2        R=+17519  p =  3e-6345    FAIL !!!!!!!!
          [Low16/64]FPF-14+6/16:cross       R= +18.0  p =  3.9e-16    FAIL !
          [Low4/64]BCFN(2+0,13-5,T)         R=+130.0  p =  4.5e-51    FAIL !!!!
          [Low4/64]BCFN(2+1,13-5,T)         R= +40.9  p =  3.4e-16    FAIL !
          [Low4/64]BCFN(2+2,13-5,T)         R=  +9.6  p =  6.3e-4   unusual
          [Low4/64]DC6-9x1Bytes-1           R= +3254  p =  2e-1883    FAIL !!!!!!!!
          [Low4/64]Gap-16:A                 R=+249.7  p =  2.0e-196   FAIL !!!!!!
          [Low4/64]Gap-16:B                 R= +1533  p =  1e-1385    FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:(0,14-1)     R= +3544  p =  1e-3140    FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:(1,14-2)     R= +2511  p =  1e-2195    FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:(2,14-2)     R= +1372  p =  1e-1199    FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:(3,14-3)     R= +1058  p =  5.1e-927   FAIL !!!!!!!
          [Low4/64]FPF-14+6/16:(4,14-4)     R=+618.7  p =  1.8e-505   FAIL !!!!!!!
          [Low4/64]FPF-14+6/16:(5,14-5)     R=+241.6  p =  3.4e-200   FAIL !!!!!!
          [Low4/64]FPF-14+6/16:(6,14-5)     R=+179.2  p =  2.1e-148   FAIL !!!!!
          [Low4/64]FPF-14+6/16:(7,14-6)     R=+159.9  p =  1.9e-122   FAIL !!!!!
          [Low4/64]FPF-14+6/16:(8,14-7)     R=+159.7  p =  6.0e-127   FAIL !!!!!
          [Low4/64]FPF-14+6/16:(9,14-8)     R=+122.3  p =  5.0e-88    FAIL !!!!
          [Low4/64]FPF-14+6/16:(10,14-8)    R= +47.9  p =  1.6e-34    FAIL !!!
          [Low4/64]FPF-14+6/16:(11,14-9)    R= +34.8  p =  3.5e-22    FAIL !!
          [Low4/64]FPF-14+6/16:(12,14-10)   R= +26.3  p =  1.5e-14    FAIL
          [Low4/64]FPF-14+6/16:all          R= +4730  p =  6e-4439    FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:all2         R=+5302901 p = 0           FAIL !!!!!!!!
          [Low4/64]FPF-14+6/16:cross        R= +5962  p =  8e-4680    FAIL !!!!!!!!
          ...and 113 test result(s) without anomalies
</pre>

                        <p>In fact, a very large number of different initialization parameters will lead to the same
                            failures. This happens because
                            modulo an additive constant <em>there are just two sequences that can be produced by an LCG
                                of the type used by PCG,
                                no matter which constant you are using</em>. The idea that different constants generate
                            truly different sequences is
                            entirely false.

                        <p>This is known at least since <a href="https://ieeexplore.ieee.org/document/718715">Durst's
                                1989 paper</a>:
                            if you have an LCG of the form <i>x</i><sub><i>n</i></sub> = <i>a</i> <i>x</i><sub><i>n</i>
                                – 1</sub> + <i>c</i>, and you take any <i>r</i>, then the generator
                            <i>y</i><sub><i>n</i></sub> = <i>a</i> <i>y</i><sub><i>n</i> – 1</sub> + <i>c</i> +
                            (<i>a</i> – 1)<i>r</i>
                            satisfies <i>x</i><sub><i>n</i></sub> = <i>y</i><sub><i>n</i></sub> – <i>r</i> for all
                            <i>n</i>. It is easy to see by induction starting from <i>x</i><sub><i>0</i></sub> =
                            <i>y</i><sub><i>0</i></sub> – <i>r</i>, as
                            <i>y</i><sub><i>n</i></sub> = <i>a</i> <i>y</i><sub><i>n</i> – 1</sub> + <i>c</i> = <i>a</i>
                            (<i>x</i><sub><i>n</i> – 1</sub> – <i>r</i>) + <i>c</i> + (<i>a</i> – 1)<i>r</i> = <i>a</i>
                            <i>x</i><sub><i>n</i> – 1</sub> + <i>c</i> – <i>r</i> = <i>x</i><sub><i>n</i></sub> –
                            <i>r</i>.

                        <p>Because of the type of constant used by PCG, called of <em>high potency</em> (it's a good
                            property in general), for every pair of constants
                            <i>c</i> and <i>d</i> such that <i>c</i> – <i>d</i> is divisible by four you can find such
                            an <i>r</i>. This divides the constants
                            in two equivalence classes, and the sequences in each class are basically the same—they
                            differ just by an additive constant. This minimal
                            difference makes the sequences massively correlated, and this correlation passes without
                            difficulty the minimum scrambling of PCG generators; hence the
                            disaster above.

                        <p>If you want to see this correlation directly, you can try a <a
                                href="pcg128diff.c">program</a> that given a state
                            and two &ldquo;independent stream&rdquo; initializers, will start two PCG 128-bit generators
                            using the
                            provided initializers: the first generator will start from the provided state, the second
                            generator from
                            the associated state in the correspondence above. The difference between the states of the
                            two
                            generators will always be the same constant, and the program just prints it as the state of
                            both generators advances:

                        <pre>
        ./pcg128diff 0x2360ed051fc65da4 0x4385df649fccf645 0x5851f42d4c957f2d 0x14057b7ef767814f 0xbf58476d1ce4e5b9 0x94d049bb133111eb
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
        0xf48e95bc9761ba1d9a9628d3d501146e
</pre>

                        <p>Two generators following the same states modulo an additive constant cannot generate
                            &ldquo;independent streams&ldquo;.
                            If you modify the program to write the output of the two generators interleaved and pipe
                            the result into PractRand, you will see a cascade of statistical failures as shown above.

                        <p>Wrap-up: <strong>Do not use PCG generators with multiple sequences</strong>.

                        <h2>Subsequences Within the Same Generator</h2>

                        <p>It is instructive to look inside a single-sequence 128-bit PCG generator
                            and consider the many possible sequences it can emit starting from
                            different initial states, similarly to what we did with &ldquo;ext&rdquo; generators. We
                            already mentioned that the sequence
                            emitted by an LCG when using a certain additive constant can be obtained from the sequence
                            for another additive constant, but in fact much more is true: the output
                            for any additive constant and any initial state can be computed easily
                            knowing the output from state 0 using the additive constant 1.

                        <p>For prime moduli, nothing particularly bad happens. But if the modulus is not prime,
                            there are consequences, and the worst consequences are for a modulus which is the power of a
                            prime, as in the PCG
                            case (which is why LCGs with moduli of the form
                            2<sup><var>n</var></sup>, as the ones used in PCG generators, are
                            considered of low quality). Essentially, changing
                            the high bits of the state has no impact on the low bits&mdash;forever.
                            And, as in the previous case, this structural defect is passed on to PCG generators.

                        <p>I put together a small <a href="intpcg128.c">C program</a>
                            which creates two 128-bit PCG generators. You provide the initial state of the first
                            generator, and the highest 64 bits of the state of the second generator; the lowest
                            64 bits will be identical to the first one. Then the program emits the output of
                            the two PCG generators, interleaved. Let us try with random arguments
                            <code>0x596d84dfefec2fc7</code>, <code>0x6b79f81ab9f3e37b</code> and
                            <code>0x8d7deae980a64ab0</code>
                            (i.e., the first PCG generator will start from state
                            <code>0x596d84dfefec2fc76b79f81ab9f3e37b</code>
                            and the second generator from state <code>0x8d7deae980a64ab06b79f81ab9f3e37b</code>) and
                            pipe into PractRand:

                        <pre>
rng=RNG_stdin64, seed=unknown
length= 16 gigabytes (2^34 bytes), time= 411 seconds
  Test Name                         Raw       Processed     Evaluation
  BCFN(0+0,13-0,T)                  R= +22.5  p =  1.3e-11   VERY SUSPICIOUS
  BCFN(0+1,13-0,T)                  R=+374.9  p =  5.5e-200   FAIL !!!!!!
  BCFN(0+2,13-0,T)                  R=+296.4  p =  5.2e-158   FAIL !!!!!
  DC6-5x4Bytes-1                    R= +85.3  p =  1.2e-51    FAIL !!!!
  ...and 1637 test result(s) without anomalies
</pre>

                        <p>In spite of half the bits of the initial state
                            being different, and in spite of having waited billion outputs
                            for the two sequences emitted by the PCG generator to decorrelate, decorrelation does not
                            happen: for every
                            sequence emitted by a PCG generator, there is a very large number of non-overlapping
                            correlated sequences
                            starting from different initial states.

                        <p>If we change just the highest 32 bits insted of the highest 64 bits the results
                            are so catastrophic to be embarrasing (I removed hundreds of lines):
                        <pre>
rng=RNG_stdin64, seed=unknown
length= 64 megabytes (2^26 bytes), time= 2.3 seconds
  Test Name                         Raw       Processed     Evaluation
  BCFN(0+0,13-3,T)                  R= +6430  p =  2e-3043    FAIL !!!!!!!!
  BCFN(0+1,13-3,T)                  R=+15707  p =  3e-7434    FAIL !!!!!!!!
  BCFN(0+2,13-3,T)                  R=+10498  p =  1e-4968    FAIL !!!!!!!!
...
  mod3n(0):(0,9-0)                  R= +33.9  p =  1.3e-18    FAIL !
  DC6-9x1Bytes-1                    R=+477.5  p =  6.1e-304   FAIL !!!!!!
  DC6-6x2Bytes-1                    R= +1872  p =  2e-1233    FAIL !!!!!!!!
  DC6-5x4Bytes-1                    R= +1618  p =  1e-1085    FAIL !!!!!!!!
  Gap-16:A                          R=+483.3  p =  2.7e-422   FAIL !!!!!!!
  Gap-16:B                          R= +1032  p =  1.9e-918   FAIL !!!!!!!
  [Low1/8]BCFN(0+0,13-5,T)          R=+352.1  p =  5.3e-138   FAIL !!!!!
  [Low1/8]BCFN(0+1,13-5,T)          R= +18.4  p =  2.2e-7   suspicious
  [Low1/8]DC6-9x1Bytes-1            R= +40.6  p =  2.3e-21    FAIL !!
  [Low1/8]DC6-6x2Bytes-1            R= +81.9  p =  9.3e-50    FAIL !!!!
  [Low1/8]DC6-5x4Bytes-1            R= +38.3  p =  3.3e-21    FAIL !!
  [Low1/8]FPF-14+6/64:(0,14-3)      R= +19.8  p =  3.7e-17    FAIL
  [Low1/8]FPF-14+6/64:(1,14-4)      R= +12.7  p =  2.7e-10  very suspicious
  [Low1/8]FPF-14+6/64:(2,14-5)      R=  +9.4  p =  1.1e-7   mildly suspicious
  [Low1/8]FPF-14+6/64:(3,14-5)      R= +17.1  p =  4.5e-14    FAIL
  [Low1/8]FPF-14+6/64:(6,14-8)      R= +14.0  p =  3.8e-10  very suspicious
  [Low1/8]FPF-14+6/64:(7,14-8)      R= +12.1  p =  9.2e-9   suspicious
...
  [Low8/64]FPF-14+6/4:(9,14-7)      R= +12.8  p =  4.8e-10  very suspicious
  [Low8/64]FPF-14+6/4:all           R=+485.3  p =  5.9e-455   FAIL !!!!!!!
  [Low8/64]FPF-14+6/4:cross         R=+289.0  p =  8.7e-228   FAIL !!!!!!
  [Low8/64]Gap-16:A                 R= +20.5  p =  5.5e-17    FAIL !
  [Low8/64]Gap-16:B                 R=+140.6  p =  4.6e-114   FAIL !!!!!
  ...and 661 test result(s) without anomalies
</pre>

                        <p>So, what's happening here? By choosing the worst case (changing just the highest bit),
                            uncommenting the <code>printf()</code> calls in the code and commenting the
                            <code>fwrite()</code> calls
                            we obtain:
                        <pre>
    78b4c0c8b39829c8
    b4c0c8b29829c84f
    07ae88d0bbb18236
    a707ae88c0bbb182
    78be926ebaf6613d
    0678be926fbaf661
    96a5ef3c1b029f66
    a5ef3c1f029f66ce
    79eec016d1e02600
    9579eec006d1e026
    78f96c30b953ccb3
    f96c30b153ccb319
    83bc05c7ee753ca1
    3583bc05d7ee753c
</pre>

                        <p>The repeated bit patterns in each pair of consecutive outputs are evident even to the naked
                            eye:
                            it is not surprising that so many tests fail. Any change in the initial state leaving intact
                            a sufficiently large number of
                            low bits will reproduce the problem: the repeated patterns will never go away.

                        <p>Instead, generators using a linear engine such as <code>xoroshiro128++</code>
                            will decorrelate after a few outputs even if a single bit of
                            state had been flipped. This is what happens with two such generators initialized
                            with exactly the same seed of the two PCG generators:
                        <pre>
    5362d8a01671b995
    d362d8a01670b995
    0a97246a7e80a888
    8a98346a7e81a88a
    c47b9b0e4dc38c7b
    44769b0c35c48b79
    4e442a6854dab254
    cb883a5234d5f0d4
    d6a29eb093c23736
    da229bd8bbdb072c
    79bb56117df4d1f9
    76b2e38b66dfd211
    39028d687f5b1025
    09737341864c79a8
</pre>

                        <p>Larger-state linear generators might require more time, but they
                            will eventually decorrelate. In fact, any sensible pseudorandom
                            generator (not necessarily linear) will mix up quickly a
                            state change (even a single-bit change) and provide an uncorrelated subsequence.

                        <p>Wrap-up: <strong>PCG generators contain a large number of pairs of non-overlapping correlated
                                subsequences</strong>.


                        <h1 id="claims">False Claims</h1>

                        <p>We are now discussing just single-sequence generators with <var>w</var>-bit output and
                            <var>w</var> or 2<var>w</var>
                            bits of state. The other options have the severe statistical flaws we discussed.

                        <h2>It is Challenging to Predict a PCG Generator</h2>

                        <p>This claim has appeared originally on Melissa O'Neill website and on her manuscript. There is
                            no evidence for this claim.
                            To me, it has been always evident that PCG generators are very easy to predict. Finally, in
                            2020 a group a INRIA
                            took up the challenge and <a href="https://hal.inria.fr/hal-02700791/">showed how to predict
                                a PCG generator</a> using standard
                            cryptoanalytic techniques.

                        <p>The paper takes a particularly strong variant and solves the problem for a generic increment
                            constant. A couple
                            of years ago I had shown similar results for another variant, assuming a fixed constant: <a
                                href="predpcg64.c">this program</a> accepts as input the 64-bit state of a PCG
                            generator, generates three outputs, and recover the original state from the output, making
                            it possible to predict
                            all future outputs of the generator.
                            Just compile with <code>-O3</code> and run.

                        <p>Writing the function that performs the
                            prediction, <code>recover()</code>, took maybe half an hour of effort. It's a couple of
                            loops, a couple of if's and
                            a few logical operations. Less than 10 lines of code.
                        <pre>
        > ./predpcg 0x333e2c3815b27604
        Provided generator state: 333e2c3815b27604
        First three outputs: cd9f107b, 8b817ffc, 7c12d316
        Recovered generator state (from output): 333e2c3815b27604
</pre>

                        <p>Analogously <a href="predpcg128.cpp">this program</a> accepts as input the 128-bit state of a
                            PCG (same variant and assumptions)
                            generator, generates a few outputs, and recovers the original state from the output, making
                            it possible again to predict
                            all future outputs of the generator. To compile it, you will need Victor Shoup's amazing <a
                                href="https://www.shoup.net/ntl/">NTL library</a>.
                            The program uses the same logic of the 64-bit case (and of the INRIA paper)&mdash;guessing
                            exhaustively a few bits, deriving a lot of other bits,
                            and solving a simple modular equation. However, in the 64-bit case the equation can be
                            solved by trying all possible solutions,
                            whereas in this case we use a standard technique based on <a
                                href="https://en.wikipedia.org/wiki/Lattice_reduction">lattice reduction</a>: as a
                            result, discovering the
                            initial state takes usually <em>less</em> time than in the 64-bit case (in fact, the
                            computation time can be
                            brought down to well below a second if you are willing to examine more outputs).

                        <p>By the same token, it is easy to set the state of a PCG generator so that it outputs a string
                            of
                            your choice. <a href="cook.c">This program</a>, for example, forces a PCG generator with 128
                            bits of state to output the string <code>> John D. Cook &lt;</code>.
                            If you pass its output through <code>hexdump -C</code>, you'll see

                        <pre>
00000000  98 06 19 c7 65 5b ce 68  f2 41 47 84 50 cf ba fa  |....e[.h.AG.P...|
00000010  a9 eb 2d 00 67 a3 34 af  5a e7 70 31 4b ae a3 38  |..-.g.4.Z.p1K..8|
00000020  03 98 b2 b5 39 0d 05 e3  98 db 33 9f b7 d4 9d b7  |....9.....3.....|
00000030  2c 29 12 34 52 66 ce b7  01 ca 96 3f f3 eb cf 7a  |,).4Rf.....?...z|
00000040  d9 76 81 e9 36 e7 06 2b  c6 94 0c 66 d0 96 d6 82  |.v..6..+...f....|
00000050  5f b1 c6 18 50 24 19 64  db 0a de 7b 27 28 ab 81  |_...P$.d...{'(..|
00000060  0f 31 0b 5c 37 bd 10 ec  1e 04 da ae 18 ce 9d 4d  |.1.\7..........M|
00000070  ff 5c fd 43 fd e6 24 70  23 94 8f 8b 41 0a 89 eb  |.\.C..$p#...A...|
00000080  3e 20 4a 6f 68 6e 20 44  2e 20 43 6f 6f 6b 20 3c  |> John D. Cook &lt;|
00000090  03 f6 4e 49 4a 39 fa 15  e1 3c 9f e7 bc 78 a9 c0  |..NIJ9...&lt;...x..|
000000a0  ea ea e2 46 65 65 63 b5  81 1b 76 01 c9 28 8b 6d  |...Feec...v..(.m|
000000b0  ec a2 0c a4 6b e1 33 d0  55 6f 8a db 49 73 a7 38  |....k.3.Uo..Is.8|
000000c0  6d 33 8c 5b 9a 88 39 ff  70 90 ff 8f 5d 0a a6 75  |m3.[..9.p...]..u|
000000d0  dd d6 2f 5c 44 bc a2 af  71 17 8a d2 f0 a0 cf da  |../\D...q.......|
000000e0  f2 3b c5 7b 51 dc 75 50  0f 50 79 8a 5b 9b 7b c4  |.;.{Q.uP.Py.[.{.|
</pre>


                        <p>Wrap-up: <strong>PCG generators are easy to predict</strong>.


                        <h2>PCG Generators are Fast</h2>

                        <p>They're not. 128-bit operations are slow. On my hardware a 128-bit PCG generators take 2.75ns
                            to
                            emit an integer, against 0.95ns for <code>xoroshiro128++</code>. The PCG generator is almost
                            <em>three times slower</em>.
                            And they both pass all statistical tests.

                        <p>You might want to measure the speed on your hardware: just download the <a
                                href="harness.c">harness</a> and the
                            <a href="pcg128-speed.c">benchmark</a>, compile the latter with
                            <code>gcc -O3 -fno-move-loop-invariants -fno-unroll-loops</code> and execute with at least a
                            billion repetitions
                            (the number of repetitions is the only parameter to the harness). You can also
                            download the <a href="xoroshiro128++-speed.c">benchmark for <code>xoroshiro128++</code></a>
                            and compare. But the idea that manipulating
                            128-bit numbers can be faster than performing a few 64-bit shifts,
                            rotations, xors and sums is ridiculous. The PCG generators with the
                            same number of state and input bits are slightly faster (1.46ns), but still
                            slower than a <code>xoroshiro128++</code> generator.

                        <p>The new design of PCG generators used in NumPy, PCG64DXSM, avoids almost all full 128-bit
                            operations and thus needs just 1.44ns to emit an integer,
                            which is a significant improvement, albeit the timings are still far from the sub-ns area of
                            scrambled linear generators or <a href="MWC256.c">64-bit MWC generators of similar size</a>.

                        <p>Said that, I will repeat again: <em>you have to measure the speed of your PRNG inside your
                                application</em>. These
                            figures are just an indication. Even better, you might want to check the
                            results of the <a
                                href="https://software.intel.com/en-us/articles/intel-architecture-code-analyzer">Intel®
                                Architecture Code Analyzer</a>, which reports <a href="xoroshiro128++-iaca.txt">3.50
                                cycles for <code>xoroshiro128++</code></a>,
                            <a href="pcg_oneseq_128_xsh_rs_64-iaca.txt">a whopping 9.53 cycles for a 128-bit PCG
                                generator</a>, and
                            <a href="pcg64dxsm-iaca.txt">5.53 cycles for PCG64DXSM</a>.


                        <p>Wrap-up: <strong>while PCG generators are not <em>terribly</em> slow, they are very far from
                                the sub-ns
                                performance of fast scrambled linear generators</strong>.

                        <h1 id="conclusions">Conclusions</h1>

                        <p>Wrap-up: <strong>There is technically no sensible reason to use a PCG generator: those
                                without flaws are not competitive</strong>.

                        <p>Nonetheless, you might wish to use at all costs, for some reason, a
                            PRNG based on congruential arithmetic with 64 bits of output, 128 bits or more of state, and
                            using 128-bit multiplications.

                        <p>In that case, you have a much better option:
                            <a
                                href="https://groups.google.com/forum/#!searchin/sci.stat.math/Yet$20another$20rng%7Csort:date/sci.stat.math/p7aLW3TsJys/QGb1kti6kN0J">Marsaglia's
                                Multiply-With-Carry generators</a>
                            and their generalizations. For example, <a href="MWC128.c">MWC128</a> is a
                            generator with 128 bits of state that is much faster than a PCG generator, and the design
                            can be extended, say, to <a href="MWC192.c">192</a> or <a href="MWC256.c">256</a> bits of
                            state.

                        <p>The generators above pass all statistical tests, but have a few theoretical defects, which
                            are
                            partially fixed by <a href="https://www.math.ias.edu/~goresky/pdf/p1-goresky.pdf">a
                                generalized version defined by Goresky and Klapper</a>:
                            <a href="GMWC128.c">GMWC128</a> and <a href="GMWC256.c">GMWC256</a>. GMWC generators,
                            however, are about twice slower than MWC generators.

                        <p>The generators with 128 bits of state have period ≈2<sup>127</sup> (more precisely, there
                            are two orbits of size ≈2<sup>127</sup>, and after very few steps you will fall into one).
                            Analogously,
                            the generators with 256 bits of state have period ≈2<sup>255</sup>.

                        <p>Subsequences of these generators will be uncorrelated except in very special cases&mdash;when
                            the initial states of the associated linear congruential
                            generator differ by a small multiplicative constant; but these cases can be easily avoided,
                            for example, by seeding the carry <code>c</code> with a fixed constant.

                    </div>

                    <div class="col-lg-3">
                        <aside class="pt-lg-4">
                            <h5 class="mt-4 mt-lg-0">C code</h5>
                            <p>
                            <ul>
                                <li><a HREF="uncpcg.cpp"><code>uncpcg.cpp</code></a>
                                <li><a HREF="corrpcg.c"><code>corrpcg.c</code></a>
                                <li><a HREF="pcg128diff.c"><code>pcg128diff.c</code></a>
                                <li><a HREF="intpcg128.c"><code>intpcg128.c</code></a>
                                <li><a HREF="predpcg64.c"><code>predpcg64.c</code></a>
                                <li><a HREF="predpcg128.cpp"><code>predpcg128.cpp</code></a> (needs <a
                                        href="https://www.shoup.net/ntl/">NTL</a>)
                                <li><a HREF="bijpcg.c"><code>bijpcg.c</code></a>
                                <li><a HREF="MWC128.c"><code>MWC128</code></a> + <a HREF="mp.c"><code>mp.c</code></a>
                                <li><a HREF="MWC256.c"><code>MWC256</code></a> + <a HREF="mp.c"><code>mp.c</code></a>
                                <li><a HREF="GMWC128.c"><code>GMWC128</code></a> + <a HREF="mp.c"><code>mp.c</code></a>
                                <li><a HREF="GMWC256.c"><code>GMWC256</code></a> + <a HREF="mp.c"><code>mp.c</code></a>
                            </ul>

                            <h5>Validation</h5>
                            <p><a href="https://validator.w3.org/check?uri=<?php echo urlencode($_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']); ?>"
                                    class="text-decoration-none">This is valid HTML5</a></p>
                        </aside>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-dark text-center py-3 mt-5 border-top border-secondary">
        <div class="container">
            <div class="row">
                <div class="col-12 text-light">
                    <p class="mb-0">&copy; <?php echo date("Y"); ?> Sebastiano Vigna. All rights reserved.</p>
                    <p class="mb-0">Built with <a href="https://getbootstrap.com/"
                            class="text-info text-decoration-none" target="_blank"
                            rel="noopener noreferrer">Bootstrap</a> and
                        <a href="https://php.net/" class="text-info text-decoration-none" target="_blank"
                            rel="noopener noreferrer">PHP</a>.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
        crossorigin="anonymous"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>

    <script>
        $(document).ready(function () {
            var $sections = $('h1[id]'); // Target h1 elements with an ID
            var $navLinks = $('.navbar-nav .nav-link');
            var navbarHeight = $('.navbar.sticky-top').outerHeight(); // Get the actual navbar height

            function getScrollMarginTop(element) {
                const computedStyle = window.getComputedStyle(element);
                // Parse the value as a float, defaulting to 0 if it's not a valid number (e.g., 'auto')
                return parseFloat(computedStyle.getPropertyValue('scroll-margin-top')) || 0;
            }

            // Function to update active link on scroll
            function updateActiveNavLink() {
                lastId = "intro";
                $sections.each(function () {
                    var sectionTop = $(this).offset().top - getScrollMarginTop(this) - navbarHeight; // Adjust for navbar height + a small buffer

                    if (sectionTop < 0) {
                        lastId = $(this).attr('id');
                    }
                });

                $navLinks.removeClass('active');
                $('a.nav-link[href="#' + lastId + '"]').addClass('active');
            }

            // Run on scroll
            document.body.addEventListener('scroll', updateActiveNavLink);

            // Enhance existing smooth scroll for internal links to immediately set active class
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    $navLinks.removeClass('active'); // Remove active from all
                    $(this).addClass('active');      // Add active to the clicked one
                });
            });

            // Initial check on page load to set the correct active link based on URL hash
            updateActiveNavLink();

            // Smooth scroll for internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1); // Remove '#'
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }); // End of $(document).ready
    </script>