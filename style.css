/* Custom CSS for the PRNG site - Clean Tech Look with Inter Font */

html,
body {
    height: 100%;
    width: 100%;
    overflow-x: hidden;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #ffffff;
    color: #1a1a1a;
    line-height: 1.6;
    font-size: 16px;
}

/* Specific styling for internal anchors with sticky header */
h1[id],
h2[id],
h3[id],
h4[id],
h5[id],
h6[id] {
    scroll-margin-top: 70px;
}

/* This accounts for the potentially much larger obstruction you observe on mobile. */
@media (max-width: 991.98px) {

    /* This media query targets screens smaller than Bootstrap's 'lg' breakpoint */
    h1[id],
    h2[id],
    h3[id],
    h4[id],
    h5[id],
    h6[id],
    div.col[id] {
        scroll-margin-top: 400px;
    }
}

h1 {
    margin-top: 3rem;
    margin-bottom: 1.5rem;
    font-size: 2.25rem;
    font-weight: 700;
}

h1.first {
    margin-top: 1rem;
}

h2 {
    font-size: 1.5rem;
    margin-top: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

h3 {
    font-size: 1.25rem;
    margin-top: 2rem;
    border-bottom: 1px solid #d1d5db;
}

/* Clean heading styles with horizontal rules */
h1,
h2,
h3,
h4,
h5,
h6 {
    color: #2563eb;
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
}

h1 {
    border-bottom: 3px solid #2563eb;
}

h2 {
    border-bottom: 2px solid #6b7280;
}

/* Paragraph styling */
p {
    margin-bottom: 1.25rem;
    line-height: 1.7;
}

/* Main content area */
main {
    background-color: #ffffff;
}

/* Add subtle section separators */
main>.container-fluid>.row>.col-lg-10>.row>.col-lg-9>h1:not(.first) {
    position: relative;
}

main>.container-fluid>.row>.col-lg-10>.row>.col-lg-9>h1:not(.first)::before {
    content: "";
    position: absolute;
    top: -1.5rem;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, #d1d5db 20%, #d1d5db 80%, transparent);
}

/* Sidebar headings */
.col-lg-3 aside h1,
.col-lg-3 aside h2,
.col-lg-3 aside h3,
.col-lg-3 aside h4,
.col-lg-3 aside h5,
.col-lg-3 aside h6 {
    font-size: 1.125rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

/* Links */
a {
    color: #2563eb;
    text-decoration: none;
    transition: all 0.2s ease-in-out;
    border-bottom: 1px solid transparent;
}

a:hover {
    color: #1d4ed8;
    border-bottom: 1px solid #2563eb;
}


/* Clean navbar styling */
.navbar {
    background-color: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.navbar-brand,
.navbar-nav .nav-link {
    color: #374151;
    font-weight: 500;
    font-family: 'Inter', sans-serif;
    transition: color 0.2s ease-in-out;
}

.navbar-brand:hover,
.navbar-nav .nav-link:hover {
    color: #2563eb;
}

.navbar-nav .nav-link.active {
    background-color: #2563eb;
    border-radius: 0.375rem;
    color: #ffffff;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* Code and pre-formatted text */
pre,
code,
samp {
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 0.875em;
    background-color: #f8fafc;
    color: #374151;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 0.25em 0.5em;
}

pre {
    padding: 1rem;
    display: block;
    overflow-x: auto;
    background-color: #f8fafc;
    border: 1px solid #e5e7eb;
    border-left: 4px solid #2563eb;
    margin: 1.5rem 0;
    position: relative;
}

pre::before {
    content: "";
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    height: 1px;
    background: linear-gradient(to right, #2563eb, transparent);
}

/* Blockquote for emphasis */
blockquote {
    border-left: 4px solid #2563eb;
    padding-left: 1rem;
    margin-left: 0;
    color: #6b7280;
    font-style: italic;
}

/* Table styling for DataTables */
.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background-color: #2563eb;
    color: #ffffff;
    border-color: #2563eb;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: #f3f4f6;
    color: #2563eb;
    border-color: #2563eb;
}

/* Custom styling for specific elements */
.alert-warning {
    background-color: #fef3c7;
    color: #92400e;
    border-color: #f59e0b;
}

.alert-info {
    background-color: #dbeafe;
    color: #1e40af;
    border-color: #3b82f6;
}

.small-caps {
    font-variant: small-caps;
}

/* Responsive table styling */
.table-responsive {
    margin-top: 2rem;
    margin-bottom: 2rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Add horizontal rules before major sections */
.table-responsive::before {
    content: "";
    display: block;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, #d1d5db, transparent);
    margin: 2rem 0 1rem 0;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}


/* General table styling */
table.dataTable {
    background-color: #ffffff;
    color: #374151;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid #e5e7eb;
}

/* Table header styling */
table.dataTable thead th,
table.dataTable thead td {
    background-color: #f8fafc;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    font-family: 'Inter', sans-serif;
}


.table-striped>tbody>tr:nth-child(odd) {
    background-color: #f9fafb;
}

.table-striped>tbody>tr:nth-child(odd):hover {
    background-color: #f3f4f6;
}

.table-striped>tbody>tr:nth-child(odd)>td {
    background-color: inherit;
    color: #374151;
    border: 1px solid #e5e7eb;
    padding: 0.75rem 1rem;
}

.table-striped>tbody>tr:nth-child(even) {
    background-color: #ffffff;
}

.table-striped>tbody>tr:nth-child(even):hover {
    background-color: #f3f4f6;
}

.table-striped>tbody>tr:nth-child(even)>td {
    background-color: inherit;
    color: #374151;
    border: 1px solid #e5e7eb;
    padding: 0.75rem 1rem;
}

/* Links within table headers */
table.dataTable thead th a,
table.dataTable thead th a:visited {
    color: #374151;
    text-decoration: none;
    border-bottom: 1px solid transparent;
}

table.dataTable thead th a:hover {
    color: #2563eb;
    border-bottom: 1px solid #2563eb;
}

/* Styling for the table caption (if used as the table title) */
table.dataTable caption {
    color: #2563eb;
    text-align: left;
    padding: 0.75rem 1rem;
    font-size: 1.25rem;
    font-weight: 600;
    font-family: 'Inter', sans-serif;
}

/* Additional styling for better visual hierarchy */

/* Improve button styling */
.btn-primary {
    background-color: #2563eb;
    border-color: #2563eb;
    font-family: 'Inter', sans-serif;
    font-weight: 500;
}

.btn-primary:hover {
    background-color: #1d4ed8;
    border-color: #1d4ed8;
}

/* Improve form styling */
.form-control {
    border-color: #d1d5db;
    font-family: 'Inter', sans-serif;
}

.form-control:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}