/* Written in 2020 by <PERSON><PERSON> (<EMAIL>)

Forces a PCG generator to output the string "> <PERSON> <".

To the extent possible under law, the author has dedicated all copyright
and related and neighboring rights to this software to the public domain
worldwide.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR
IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE. */


#include <stdio.h>
#include <stdint.h>

typedef __uint128_t uint128_t;

// -------
// PCG code Copyright by <PERSON> O'<PERSON>

typedef __uint128_t pcg128_t;
#define PCG_128BIT_CONSTANT(high,low) ((((pcg128_t)high) << 64) + low)

struct pcg_state_128 {
	pcg128_t state;
};

#define PCG_DEFAULT_MULTIPLIER_128 \
        PCG_128BIT_CONSTANT(2549297995355413924ULL,4865540595714422341ULL)
#define PCG_DEFAULT_INCREMENT_128  \
        PCG_128BIT_CONSTANT(6364136223846793005ULL,1442695040888963407ULL)

static inline void pcg_oneseq_128_step_r(struct pcg_state_128* rng) {
	rng->state = rng->state * PCG_DEFAULT_MULTIPLIER_128 + PCG_DEFAULT_INCREMENT_128;
}

static inline uint64_t pcg_output_xsh_rs_128_64(pcg128_t state) {
	return (uint64_t)(((state >> 43u) ^ state) >> ((state >> 124u) + 45u));
}

static inline uint64_t pcg_oneseq_128_xsh_rs_64_random_r(struct pcg_state_128* rng) {
	pcg_oneseq_128_step_r(rng);
	return pcg_output_xsh_rs_128_64(rng->state);
}

int main(int argc, char* argv[]) {
	struct pcg_state_128 rng;
	rng.state = (__uint128_t)0x216c549ced70826b << 64 | 0x1b900f7fadcc7c16;
	for(;;) {
		uint64_t output = pcg_oneseq_128_xsh_rs_64_random_r(&rng);
		fwrite(&output, sizeof(output), 1, stdout);
	}
}
